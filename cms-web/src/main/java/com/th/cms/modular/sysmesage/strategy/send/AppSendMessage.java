package com.th.cms.modular.sysmesage.strategy.send;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.stylefeng.roses.kernel.model.enums.YesOrNotEnum;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.th.cms.core.util.OptionalUtil;
import com.th.cms.modular.influcer.bizInflucer.model.BizInflucer;
import com.th.cms.modular.influcer.influcerDevices.model.notification.PushNotificationDTO;
import com.th.cms.modular.influcer.influcerDevices.service.BizInflucerDevicesService;
import com.th.cms.modular.influcer.influcerMessage.dao.BizInflucerMessageMapper;
import com.th.cms.modular.influcer.influcerMessage.model.BizInflucerMessage;
import com.th.cms.modular.sysmesage.consts.SendType;
import com.th.cms.modular.sysmesage.dto.MessageRequestDTO;
import com.th.cms.modular.sysmesage.entity.SysMessageSendLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 官方文档
 * https://docs.jiguang.cn/jpush/intro/product_archit
 * https://docs.jiguang.cn/jpush/server/push/server_overview
 * https://go48pg.yuque.com/go48pg/pa41sm/ruxieh?
 * https://docs.jiguang.cn/jpush/server/server_sdk
 * https://go48pg.yuque.com/go48pg/pa41sm/sli72e
 *
 * 项目接口
 * cms-web
 * /bizInflucer/bizInflucer_push 选择达人，跳转到push页面
 * /bizInflucer/push   推送消息接口
 *
 * th-api
 * /user/influcerDeviceDiscovery     apk初始化，获取设备极光id
 */
@Component
@Slf4j
public class AppSendMessage extends MessageAbstractService implements IMessageHandler {
    @Autowired
    private BizInflucerMessageMapper bizInflucerMessageMapper;
    @Resource
    private BizInflucerDevicesService bizInflucerDevicesService;

    /**
     * 发送消息
     *
     * @param requestDTO 请求参数
     */
    @Override
    public void sendMessage(MessageRequestDTO requestDTO) {
        log.info("APP push 发送开始 {}", JSONObject.toJSONString(requestDTO));
        doPush(requestDTO);
    }

    private BizInflucerMessage convertDo(BizInflucer user, MessageRequestDTO requestDTO) {
        BizInflucerMessage bizInflucerMessage = new BizInflucerMessage();
        bizInflucerMessage.setInflucerId(user.getId());
        bizInflucerMessage.setTitle(requestDTO.getTemplateDTO().getSysMessageTemplate().getTitle());
        bizInflucerMessage.setContext(requestDTO.getTemplateDTO().getRealContent());
        bizInflucerMessage.setMessageId(null);
        bizInflucerMessage.setStatus(YesOrNotEnum.Y.getCode());
        bizInflucerMessage.setUpdateTime(new Date());
        bizInflucerMessage.setMsgType(SendType.APP_PUSH.getCode());
        return bizInflucerMessage;


    }

    public void doPush(MessageRequestDTO requestDTO) {
        for (BizInflucer user : OptionalUtil.defaultList(requestDTO.getInflucers())) {
            SysMessageSendLog sysMessageSendLog = convertToSendLog(user, requestDTO);
            try {
                PushNotificationDTO dto = new PushNotificationDTO();
                dto.setInflucerIds(Lists.newArrayList(user.getId()));
                dto.setTitle(requestDTO.getTemplateDTO().getSysMessageTemplate().getTitle());
                dto.setMessage(requestDTO.getTemplateDTO().getRealContent());
                JSONObject jsonObject = bizInflucerDevicesService.pushToInflucers(dto);
                if (jsonObject == null) {
                    String errorMsg = String.format(" push app message error ====> %s", JSONObject.toJSONString(requestDTO));
                    log.error(errorMsg);
                    sysMessageSendLog.setErrorMsg(errorMsg);
                    continue;
                }
                String sendNo = jsonObject.getString("sendno");
                String msgId = jsonObject.getString("msg_id");
                //发送成功
                if ("0".equals(sendNo)) {
                    BizInflucerMessage bizInflucerMessage = convertDo(user, requestDTO);
                    bizInflucerMessage.setMessageId(msgId);
                    bizInflucerMessageMapper.insert(bizInflucerMessage);
                } else {
                    String errorMsg = JSONObject.toJSONString(jsonObject);
                    log.error(errorMsg);
                    sysMessageSendLog.setErrorMsg(errorMsg);
                }
            } catch (Exception e) {
                log.error("", e);
                sysMessageSendLog.setErrorMsg(ExceptionUtil.getMessage(e));
            } finally {
                saveSendLog(sysMessageSendLog);
            }

        }

    }
}
