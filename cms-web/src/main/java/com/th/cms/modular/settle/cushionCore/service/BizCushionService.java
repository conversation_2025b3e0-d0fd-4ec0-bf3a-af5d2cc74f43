package com.th.cms.modular.settle.cushionCore.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.stylefeng.roses.core.util.DateUtils;
import com.th.cms.core.common.IdGenerater;
import com.th.cms.core.exceptions.BusiException;
import com.th.cms.core.util.CollectionUtils;
import com.th.cms.core.util.ExcelParseUtils;
import com.th.cms.core.util.MD5FileUtil;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.enums.*;
import com.th.cms.modular.influcer.bizInflucer.model.BizInflucer;
import com.th.cms.modular.influcer.bizInflucer.service.BizInflucerService;
import com.th.cms.modular.influcer.bizInflucerBusi.model.BizInflucerBusi;
import com.th.cms.modular.influcer.bizInflucerBusi.service.BizInflucerBusiExtService;
import com.th.cms.modular.influcer.bizInflucerBusi.service.BizInflucerBusiService;
import com.th.cms.modular.influcer.bizInflucerPlatform.model.BizInflucerPlatform;
import com.th.cms.modular.influcer.bizInflucerPlatform.service.BizInflucerPlatformExtService;
import com.th.cms.modular.influcer.bizInflucerPlatform.service.BizInflucerPlatformService;
import com.th.cms.modular.influcer.bizInflucerProject.model.BizInflucerProject;
import com.th.cms.modular.influcer.bizInflucerProject.service.BizInflucerProjectService;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.service.BizPlatformService;
import com.th.cms.modular.oss.service.AliUploadOssImg;
import com.th.cms.modular.settle.contract.entity.SettleContract;
import com.th.cms.modular.settle.contract.service.impl.SettleContractServiceImpl;
import com.th.cms.modular.settle.settleAccount.model.SettleAccount;
import com.th.cms.modular.settle.settleAccount.service.SettleAccountService;
import com.th.cms.modular.settle.cushionBatch.model.CushionBatch;
import com.th.cms.modular.settle.settleBatch.plat.pdd.CommonSettleFiledModel;
import com.th.cms.modular.settle.cushionBatch.service.CushionBatchService;
import com.th.cms.modular.settle.settleCore.common.SettleCoreConstant;
import com.th.cms.modular.settle.settleCore.excelTemplate.*;
import com.th.cms.modular.settle.cushionCore.vo.CushionAddReq;
import com.th.cms.modular.settle.cushionInfluencerOrder.model.CushionInfluencerOrder;
import com.th.cms.modular.settle.cushionInfluencerOrder.service.CushionInfluencerOrderService;
import com.th.cms.modular.settle.cushionOrder.model.CushionOrder;
import com.th.cms.modular.settle.cushionOrder.service.CushionOrderExtService;
import com.th.cms.modular.settle.cushionOrder.service.CushionOrderService;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.settle.settleProjects.service.SettleProjectsExtService;
import com.th.cms.modular.settle.settleProjects.service.SettleProjectsService;
import com.th.cms.modular.settle.settleTemplateExcel.model.SettleTemplateExcel;
import com.th.cms.modular.settle.settleTemplateExcel.service.SettleTemplateExcelExtService;
import com.th.cms.modular.settle.settleTemplateField.model.SettleTemplateField;
import com.th.cms.modular.system.entity.User;
import com.th.cms.modular.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年04月02日 19:18
 */
@Component
@Slf4j
public class BizCushionService {
    @Autowired
    SettleProjectsExtService settleProjectsExtService;
    @Autowired
    CushionInfluencerOrderService cushionInfluencerOrderService;

    @Autowired
    BizInflucerPlatformService bizInflucerPlatformService;
    @Autowired
    BizInflucerBusiService bizInflucerBusiService;

    @Autowired
    CushionBatchService cushionBatchService;

    @Autowired
    SettleTemplateExcelExtService settleTemplateExcelExtService;

    @Autowired
    CushionOrderExtService cushionOrderExtService;

    @Autowired
    BizInflucerBusiExtService bizInflucerBusiExtService;

    @Autowired
    BizInflucerPlatformExtService bizInflucerPlatformExtService;

    @Autowired
    BizInflucerService bizInflucerService;

    @Autowired
    SettleProjectsService settleProjectsService;

    @Autowired
    CushionBatchApprovalService cushionBatchApprovalService;

    @Autowired
    CushionOrderService cushionOrderService;

    @Autowired
    UserService userService;

    @Autowired
    SettleContractServiceImpl settleContractService;

    private Pair<SettleTemplateExcel, List<CommonSettleFiledModel>> getCommonExclist(String jisuanFile) throws Exception {
        Pair<SettleTemplateExcel, List<SettleTemplateField>> tmpFlds = settleTemplateExcelExtService.findTmpFlds(jisuanFile);

        List<CommonSettleFiledModel> pddExlist = new ArrayList<>();
        return new Pair<>(tmpFlds.getKey(), pddExlist);
    }

    private SettleProjects getById(Long id){
        return settleProjectsService.getById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addCushionBath(CushionAddReq cushionAddReq) {
        Long projectId = cushionAddReq.getProject();
        if (projectId != null) {
            SettleProjects settleProjects = settleProjectsExtService.queryById(projectId);
            File tmpFile = null;
            try {
                String platId = settleProjects.getPlatformId();
                if (StringUtils.isNotBlank(platId)) {
                    //根据平台  整理 解析excel
                }
                //查看本项目大的垫付单
                CushionOrder cushionOrder = cushionOrderExtService.queryByProjectId(projectId);
                if (cushionOrder == null) {
                    cushionOrder = addCushionOrder(settleProjects, cushionAddReq);
                }

                //TODO,修改为通用解析方法
                Pair<SettleTemplateExcel, List<CommonSettleFiledModel>> fieldPar = getCommonExclist(cushionAddReq.getJiesuanFile());
                SettleTemplateExcel settleTemplateExcel = fieldPar.getKey();
                String batchNo = genBatchNo(settleProjects);

                BizPlatform bizPlatform = bizPlatformService.getById(settleProjects.getPlatformId());
                
                //本地测试临时使用这行
                tmpFile = AliUploadOssImg.saveToTmpFullPath(new File("").getAbsolutePath()+File.separator+ AliUploadOssImg.getFileNameFromUrl(cushionAddReq.getJiesuanFile()), AliUploadOssImg.downloadFile(cushionAddReq.getJiesuanFile(), true));

                //文件去重操作
                String fileMd5 = MD5FileUtil.getFileMD5(tmpFile);

                CushionBatch cushionBatch = saveCushion(cushionAddReq, settleProjects, platId, cushionOrder, fieldPar, settleTemplateExcel, batchNo, bizPlatform, fileMd5);
                //触发审批流
                cushionBatchApprovalService.createProjectApprovalReq(cushionBatch);

            } catch (BusiException e) {
                throw new BusiException("文件解析错误:"+e.getMessage(), e);
            }catch (IllegalArgumentException e) {
                throw new BusiException("文件解析错误:"+e.getMessage(), e);
            } catch (Exception e) {
                throw new BusiException("文件解析错误", e);
            }finally {
                FileUtil.del(tmpFile);
            }
        }
    }

    @Autowired
    private BizInflucerProjectService bizInflucerProjectService;


    private CushionBatch saveCushion(CushionAddReq cushionAddReq,
                                   SettleProjects settleProjects,
                                   String platId,
                                   CushionOrder cushionOrder,
                                   Pair<SettleTemplateExcel, List<CommonSettleFiledModel>> fieldPar,
                                   SettleTemplateExcel settleTemplateExcel,
                                   String batchNo,
                                   BizPlatform bizPlatform,
                                   String fileMd5) {
        CushionBatch cushionBatch = null;
        SettleTemplateExcel templateExcel = fieldPar.getKey();

        File tmpFile = null;
        try {
            tmpFile = AliUploadOssImg.saveToTmpFullPath(new File("").getAbsolutePath()+File.separator+ AliUploadOssImg.getFileNameFromUrl(cushionAddReq.getJiesuanFile()), AliUploadOssImg.downloadFile(cushionAddReq.getJiesuanFile(), true));
        } catch (Exception e) {
            log.error("下载文件失败",e);
            FileUtil.del(tmpFile);
            throw new BusiException("下载文件失败");
        }
        try {
            //平台配置的时候需要跟枚举的配置对应上
            ProjectSettleStrategyEnum settleStrategyEnum = ProjectSettleStrategyEnum.fromPlatformProject(templateExcel.getPlatName());
            if (settleStrategyEnum == null){
                settleStrategyEnum = ProjectSettleStrategyEnum.fromPlatformId(Integer.parseInt(platId));
            }
            if(settleStrategyEnum == null) {
                throw new BusiException("未能识别到模板类型，请检查上传文件");
            }
            switch (settleStrategyEnum){
                case TAOBAO_ZB_DF: //淘宝直播-垫付
                    long cnum = cushionBatchService.lambdaQuery().eq(CushionBatch::getProjectId,settleProjects.getId()).eq(CushionBatch::getJisuanFileMd5,fileMd5).count();
                    if(cnum > 0){
                        throw new BusiException("该垫付文件 " + cushionAddReq.getJiesuanFile() + " 已经存在");
                    }

                    //解析文件内容
                    List<CommonSettleFiledModel> commonSettleFiledModelList = analysisFile(settleStrategyEnum, tmpFile);

                    //按达人昵称汇总
                    Map<String, CommonSettleFiledModel> groupModelMap = new HashMap<>();
                    //总金额
                    BigDecimal totalRebateAmount = BigDecimal.ZERO;
                    for (CommonSettleFiledModel item : commonSettleFiledModelList) {
                        String influencerNickname = item.getInfluencerNickname();
                        if(StringUtils.isEmpty(influencerNickname) || SettleCoreConstant.INVALID_INFLUENCER_NICKNAME.equals(influencerNickname)) {
                            continue;
                        }
                        CommonSettleFiledModel groupItem = groupModelMap.get(influencerNickname);
                        if(groupItem == null) {
                            groupItem = new CommonSettleFiledModel();
                            groupItem.setInfluencerNickname(influencerNickname);
                            groupItem.setRebateAmount(BigDecimal.ZERO);
                            groupModelMap.put(influencerNickname, groupItem);
                        }
                        groupItem.setRebateAmount(groupItem.getRebateAmount().add(item.getRebateAmount()));
                        totalRebateAmount = totalRebateAmount.add(item.getRebateAmount());
                    }
                    List<CommonSettleFiledModel> groupModelList = new ArrayList<>(groupModelMap.values());

                    //关联达人信息
                    List<BizInflucerProject> bizInflucerPlatforms = bizInflucerProjectService.lambdaQuery()
                            .in(BizInflucerProject::getNickPlatid, groupModelMap.keySet())
                            .eq(BizInflucerProject::getProjectId, settleProjects.getId())
                            .eq(BizInflucerProject::getPlatformId, settleProjects.getPlatformId())
                            .list();

                    Map<String, BizInflucerProject> byNicknameMap = new HashMap<>();
                    Set<Long> influcerIds = new HashSet<>();
                    for (BizInflucerProject bizInflucerProject : bizInflucerPlatforms) {
                        byNicknameMap.put(bizInflucerProject.getNickPlatid(), bizInflucerProject);
                        influcerIds.add(bizInflucerProject.getInflucerId());
                    }
                    List<SettleContract> settleContractList = settleContractService.lambdaQuery().in(SettleContract::getInfluencerId, influcerIds).in(SettleContract::getContractStatus, 1, 2).list();
                    Assert.notEmpty(settleContractList, "未匹配到对应的达人的合同信息");
                    Set<Object> contractInflucerIdList = settleContractList.stream().map(x -> x.getInfluencerId()).collect(Collectors.toSet());
                    //查询达人信息
                    Map<Long, BizInflucer> bizInflucerMap = new HashMap<>();
                    Set<Long> dongjieAccountIds = new HashSet<>();
                    if(CollectionUtils.isNotEmpty(influcerIds)) {
                        List<BizInflucer> bizInflucerList = bizInflucerService.lambdaQuery().in(BizInflucer::getId, influcerIds).list();
                        bizInflucerMap = bizInflucerList.stream().collect(Collectors.toMap(BizInflucer::getId, Function.identity(), (v1, v2) -> v1));
                        List<SettleAccount> freezeAccountList = settleAccountService.lambdaQuery().in(SettleAccount::getUserId, influcerIds).eq(SettleAccount::getAccountStatus, SettleAccountStatus.dongjie.getCode()).list();
                        if(CollectionUtils.isNotEmpty(freezeAccountList)) {
                            dongjieAccountIds = freezeAccountList.stream().map(SettleAccount::getUserId).collect(Collectors.toSet());
                        }
                    }

                    User user = userService.getById(settleProjects.getBusinessOwner());

                    //构建基础达人订单垫付信息
                    List<CushionInfluencerOrder> cushionInfluencerOrderList = new ArrayList<>();
                    //达人特殊比例分成
                    Map<String, BigDecimal> influcerRatioMap = new HashMap<>();
                    int feibiaoCount = 0;
                    int abnormalCount = 0;
                    
                    for (CommonSettleFiledModel model : groupModelList) {
                        BizInflucerProject bizInflucerProject = byNicknameMap.get(model.getInfluencerNickname());
                        BizInflucer bizInflucer = null;
                        Integer feibiaoFlg = InflFeibiaoType.nomal.getCode();
                        
                        if(bizInflucerProject != null) {
                            if (bizInflucerProject.getInflucerId() == null || !contractInflucerIdList.contains(bizInflucerProject.getInflucerId())){
                                //如果没有对应的合同信息，跳过该条数据
                                continue;
                            }
                            bizInflucer = bizInflucerMap.get(bizInflucerProject.getInflucerId());
                            if(bizInflucerProject.getInflucerRatio() != null) {
                                feibiaoFlg = InflFeibiaoType.feibiao.getCode();
                                influcerRatioMap.put(model.getInfluencerNickname(), bizInflucerProject.getInflucerRatio());
                                feibiaoCount++;
                            }
                            if(bizInflucer != null) {
                                if(dongjieAccountIds.contains(bizInflucer.getId())) {
                                    //异常账户不保存到正常数据中
                                    abnormalCount++;
                                    continue;
                                }
                            }
                        }
                        CushionInfluencerOrder cushionInfluencerOrder = cvtInfOrder(settleProjects, batchNo, cushionAddReq, model, user, bizInflucerProject, bizInflucer);
                        cushionInfluencerOrder.setFeibiaoFlg(feibiaoFlg);
                        cushionInfluencerOrderList.add(cushionInfluencerOrder);
                    }
                    if(CollectionUtils.isEmpty(cushionInfluencerOrderList)) {
                        throw new BusiException("导入的所有数据都没有匹配到账户状态正常的达人，请检查后再导入。");
                    }

                    //构建基础垫付批次信息
                    cushionBatch = buildCushionBatch(fileMd5, settleProjects, cushionAddReq, batchNo, cushionOrder.getId());

                    cushionBatch.setLiushuiAmount(totalRebateAmount);
                    cushionBatch.setYifaAmount(0D);//总已发金额,财务审核之后才更新
                    cushionBatch.setWithdrawnAmount(BigDecimal.ZERO);
                    cushionBatch.setUnwithdrawnAmount(BigDecimal.ZERO);
                    cushionBatch.setArriveAmount(BigDecimal.ZERO);
                    cushionBatch.setNoarriveAmount(BigDecimal.ZERO);
                    cushionBatch.setAgencyRevenue(AgencyRevenueEnum.no.getCode());
                    cushionBatch.setCushionPnum(cushionInfluencerOrderList.size());//总参与垫付人数
                    ShiroUtils.setAddAuthInfo(cushionBatch);

                    //项目中的达人分成
                    Double commissionToDaren = settleProjects.getCommissionToDaren();
                    BigDecimal commissionToDarenBD = BigDecimal.valueOf(commissionToDaren).divide(BigDecimal.valueOf(100), 6, RoundingMode.DOWN);
                    BigDecimal realinflucerRatio;
                    boolean influcerRatioFlag;
                    //总应发金额
                    BigDecimal tbTotalYingfaAmount = BigDecimal.ZERO;
                    BigDecimal tbTotalLiuShuiAmount = BigDecimal.ZERO;
                    BigDecimal tbTotalFeibiaoAmount = BigDecimal.ZERO;

                    //按模板设置对应字段
                    switch (settleStrategyEnum) {
                        case TAOBAO_ZB_DF: //淘宝直播-垫付
                            for (CushionInfluencerOrder cushionInfluencerOrder : cushionInfluencerOrderList) {
                                //达人（垫付）收益=分组后总额*达人分成比*50%
                                BigDecimal influcerRatio = influcerRatioMap.get(cushionInfluencerOrder.getInfluencerName());
                                realinflucerRatio = ObjectUtils.defaultIfNull(influcerRatio, commissionToDarenBD);
                                influcerRatioFlag = influcerRatio != null;

                                tbTotalLiuShuiAmount = tbTotalLiuShuiAmount.add(cushionInfluencerOrder.getEarningsAmount());
                                BigDecimal payableAmount = BigDecimal.ZERO;
                                if (Objects.nonNull(cushionInfluencerOrder.getEarningsAmount()) && BigDecimal.ZERO.compareTo(cushionInfluencerOrder.getEarningsAmount()) < 0){
                                    payableAmount = cushionInfluencerOrder.getEarningsAmount().multiply(realinflucerRatio).multiply(BigDecimal.valueOf(0.5)).setScale(2, RoundingMode.DOWN);
                                }
                                cushionInfluencerOrder.setPayableAmount(payableAmount);
                                cushionInfluencerOrder.setUnwithdrawnAmount(payableAmount);
                                tbTotalYingfaAmount = tbTotalYingfaAmount.add(payableAmount);
                                if(influcerRatioFlag) {
                                    //统计非标金额
                                    tbTotalFeibiaoAmount = tbTotalFeibiaoAmount.add(payableAmount);
                                }
                            }
                            //垫付不计算机构收益
                            cushionBatch.setOrganizationAmount(BigDecimal.ZERO);
                            break;
                    }
                    cushionBatch.setYingfaAmount(tbTotalYingfaAmount.doubleValue());
                    cushionBatch.setLiushuiAmount(tbTotalLiuShuiAmount);
                    cushionBatch.setFeibiaoAmount(tbTotalFeibiaoAmount);
                    Assert.isTrue(tbTotalYingfaAmount.compareTo(BigDecimal.ZERO) > 0, "总应发金额必须大于0,请检查下模版数据");
                    cushionBatch.setFeibiaoAmountRate(tbTotalFeibiaoAmount.divide(tbTotalYingfaAmount,2, RoundingMode.HALF_UP).setScale(4, RoundingMode.HALF_UP));
                    cushionBatch.setFeibiaoPnum(feibiaoCount);
                    BigDecimal feibiaoRate = BigDecimal.valueOf(feibiaoCount)
                            .divide(BigDecimal.valueOf(cushionInfluencerOrderList.size()), 2, RoundingMode.HALF_UP)
                            .setScale(4, RoundingMode.HALF_UP);
                    cushionBatch.setFeibiaoRate(feibiaoRate.doubleValue());

                    //保存垫付批次信息
                    cushionBatchService.save(cushionBatch);
                    //保存达人订单垫付信息
                    log.info("保存开始项目[" + settleProjects.getId() + "]垫付批次[" + batchNo + "]达人订单垫付信息");
                    if (cushionInfluencerOrderList.size() > 0) {
                        cushionInfluencerOrderService.saveBatch(cushionInfluencerOrderList,2000);
                        log.info("保存结束项目[" + settleProjects.getId() + "]垫付批次[" + batchNo + "]达人订单垫付信息");
                    }
                    return cushionBatch;
                default:
                    throw new BusiException("[" + settleStrategyEnum.getPlatformProject() + "]不支持垫付");
            }
        }finally {
            FileUtil.del(tmpFile);
        }
    }

    //解析上传文件内容
    private List<CommonSettleFiledModel> analysisFile(ProjectSettleStrategyEnum settleStrategyEnum, File tmpFile) {
        List<CommonSettleFiledModel> modelList = new ArrayList<>();
        switch (settleStrategyEnum) {
            case TAOBAO_ZB_DF: //淘宝直播-垫付
                List<TaoBaoZhiBoDianFuSettleExcel> taoBaoZhiBoDianFuList = ExcelParseUtils.parse(tmpFile, TaoBaoZhiBoDianFuSettleExcel.class);
                for (TaoBaoZhiBoDianFuSettleExcel item : taoBaoZhiBoDianFuList) {
                    modelList.add(item.buildCommonSettleFiledModel());
                }
                break;
        }
        return modelList;
    }

    @Autowired
    private SettleAccountService settleAccountService;

    private Map<Long, BizInflucer> queryInInflcerMap(List<BizInflucerPlatform> bizInflucerPlatformList, String patId) {
        List<Long> infcIds = bizInflucerPlatformList.stream().map(BizInflucerPlatform::getInflucerId).collect(Collectors.toList());
        if(infcIds.size()>0){
            Map<Long, BizInflucer> influcerMap = bizInflucerService.lambdaQuery().in(BizInflucer::getId, infcIds).list().stream().collect(Collectors.toMap(BizInflucer::getId, t -> t));
            return influcerMap;
        }
        return new HashMap<>();
    }

    private Map<String, BizInflucerBusi> queryInBusMap(Map<Long, BizInflucer> influcerMap, String platId) {

        Set<Long> infidSet = influcerMap.keySet();
        List<String> dlist = infidSet.stream().map(t -> {
            String infIdstr = bizInflucerBusiExtService.getBizInfBusId(t + "", platId);
            return infIdstr;
        }).collect(Collectors.toList());
        if (dlist.size() > 0) {
            List<BizInflucerBusi> bizInflucerBusis = bizInflucerBusiExtService.queryInfsBusList(dlist);
            Map<String, BizInflucerBusi> influcerBusiMap = bizInflucerBusis.stream().collect(Collectors.toMap(BizInflucerBusi::getInflucerBusid, t -> t));
            return influcerBusiMap;
        }

        return new HashMap<>();
    }


    private CushionOrder addCushionOrder(SettleProjects settleProjects, CushionAddReq cushionAddReq) {
        CushionOrder cushionOrder = new CushionOrder();
        cushionOrder.setProjectId(settleProjects.getId());
        cushionOrder.setProjectName(settleProjects.getProjectName());
        cushionOrder.setCompanyTalentRatio(settleProjects.getCommissionRate());
//
//        cushionOrder.setCushionPnum();
//        cushionOrder.setLiushuiAmount();
//        cushionOrder.setYingfaAmount();
//        cushionOrder.setYifaAmount();
//        cushionOrder.setWithdrawnAmount();
//        cushionOrder.setUnwithdrawnAmount();
//        cushionOrder.setArriveAmount();
//        cushionOrder.setNoarriveAmount();
//        cushionOrder.setFeibiaoPnum();
//        cushionOrder.setFeibiaoRate();
//        cushionOrder.setFeibiaoAmount();
//        cushionOrder.setFeibiaoAmountRate();
//        cushionOrder.setBatchStatus();
//        cushionOrder.setBatchStatusName();
        cushionOrder.setJisuanFile(cushionAddReq.getJiesuanFile());
        cushionOrder.setFujianFiles(cushionAddReq.getFuzhuFiles());
        cushionOrder.setRemark(cushionAddReq.getRemark());

        cushionOrder.setUpdateTime(new Date());
        cushionOrder.setCreateTime(new Date());

        ShiroUtils.setAddAuthInfo(cushionOrder);

        cushionOrderService.save(cushionOrder);
        return cushionOrder;
    }

    @NotNull
    private CushionBatch buildCushionBatch(String fmd5, SettleProjects settleProjects, CushionAddReq cushionAddReq, String batchNo, Integer orderId) {
        CushionBatch cushionBatch = new CushionBatch();
        cushionBatch.setBatchNo(batchNo);
        cushionBatch.setProjectId(settleProjects.getId());
        cushionBatch.setProjectName(settleProjects.getProjectName());
        cushionBatch.setZhouqiStart(cushionAddReq.getZhouqiStart());
        cushionBatch.setOrderId(new Long(orderId));
        cushionBatch.setZhouqiEnd(cushionAddReq.getZhouqiEnd());
        cushionBatch.setJisuanFileMd5(fmd5);
        cushionBatch.setCompanyTalentRatio(settleProjects.getCommissionRate());
        cushionBatch.setBatchStatus(CushionBatchStatus.ShengPiing.getCode());
        cushionBatch.setBatchStatusName(CushionBatchStatus.ShengPiing.getName());
        cushionBatch.setJisuanFile(cushionAddReq.getJiesuanFile());
        cushionBatch.setFujianFiles(cushionAddReq.getFuzhuFiles());
        cushionBatch.setRemark(cushionAddReq.getRemark());
        cushionBatch.setAgencyRevenue(settleProjects.getAgencyRevenue());
        cushionBatch.setUpdateTime(new Date());
        cushionBatch.setCreateTime(new Date());
        return cushionBatch;
    }


    /**
     * 计算分成
     * @param settleProjects
     * @param batchNo
     * @param cushionAddReq
     * @param commonSettleFiledModel
     * @param bizInflucerBusi
     * @param bizInflucerProject
     * @param bizInflucer
     * @return
     */
    public CushionInfluencerOrder cvtInfOrder(SettleProjects settleProjects, String batchNo,
                                             CushionAddReq cushionAddReq,
                                             CommonSettleFiledModel commonSettleFiledModel,
                                             User bizInflucerBusi,
                                             BizInflucerProject bizInflucerProject,
                                             BizInflucer bizInflucer
    ) {
        CushionInfluencerOrder cushionInfluencerOrder = new CushionInfluencerOrder();
        //没用了
//        Double toComp = cushionProjects.getCommissionToComp();
//        Double toDaren = cushionProjects.getCommissionToDaren();
        //todo 找不到单门处理 产品

        BigDecimal liushuiAmt = commonSettleFiledModel.getRebateAmount();
        if (bizInflucer != null) {
            cushionInfluencerOrder.setInfluencerName(commonSettleFiledModel.getInfluencerNickname());
            cushionInfluencerOrder.setInfluencerId(bizInflucer.getId() + "");
            cushionInfluencerOrder.setPhoneNumber(bizInflucer.getLoginTel());
        } else {
            cushionInfluencerOrder.setInfluencerName("MISS");
            cushionInfluencerOrder.setInfluencerId("0");
            cushionInfluencerOrder.setPhoneNumber("MISS");
        }
        //todo 商务信息
        if (bizInflucerBusi != null) {
            cushionInfluencerOrder.setWorkArea(bizInflucerBusi.getDeptName());
            cushionInfluencerOrder.setGroupName(bizInflucerBusi.getDeptName());
            cushionInfluencerOrder.setBusinessContact(bizInflucerBusi.getName());
        }


        cushionInfluencerOrder.setIsArrived(InflOrderYifStatus.weifa.getCode());
        cushionInfluencerOrder.setInflStatus(InflOrderStatus.shhe.getCode());
        cushionInfluencerOrder.setInflStatusName(InflOrderStatus.shhe.getName());
        cushionInfluencerOrder.setFeibiaoFlg(InflFeibiaoType.nomal.getCode());
        cushionInfluencerOrder.setProjectId(settleProjects.getId());
        cushionInfluencerOrder.setProjectName(settleProjects.getProjectName());
        cushionInfluencerOrder.setCompanyTalentRatio(settleProjects.getCommissionRate());
        cushionInfluencerOrder.setCushionmentBatch(batchNo);

        cushionInfluencerOrder.setPlatformNickname(settleProjects.getPlatform());
        cushionInfluencerOrder.setPlatformId(settleProjects.getPlatformId());

        cushionInfluencerOrder.setEarningsAmount(liushuiAmt);
//        Double toDarenAmt = DoubleUtil.multiplicationDownDouble(liushuiAmt, toDaren * 0.01d);
        cushionInfluencerOrder.setPayableAmount(commonSettleFiledModel.getInfluencerAmount());
        cushionInfluencerOrder.setWithdrawnAmount(BigDecimal.ZERO);
        cushionInfluencerOrder.setUnwithdrawnAmount(commonSettleFiledModel.getInfluencerAmount());
        cushionInfluencerOrder.setArrivalIssue("");


        cushionInfluencerOrder.setZhouqiStart(cushionAddReq.getZhouqiStart());
        cushionInfluencerOrder.setZhouqiEnd(cushionAddReq.getZhouqiEnd());

        cushionInfluencerOrder.setUpdateTime(new Date());
        cushionInfluencerOrder.setCreateTime(new Date());

        ShiroUtils.setAddAuthInfo(cushionInfluencerOrder);

        return cushionInfluencerOrder;
    }

    @Autowired
    BizPlatformService bizPlatformService;
    @Autowired
    IdGenerater idGenerater;

    private String genBatchNo(SettleProjects settleProjects) {
        String platformId = settleProjects.getPlatformId();
        BizPlatform bizPlatform = bizPlatformService.getById(platformId);

        String jianxie = "";
        if (bizPlatform != null && StringUtils.isNotBlank(bizPlatform.getJiancheng())) {
            jianxie = bizPlatform.getJiancheng();
        }

        String dateStr = DateUtils.getyymmCurrentDate();
        //th-pdd20250328
        Long seqNum = idGenerater.genSeqNo(jianxie + dateStr, DateTimeConstants.SECONDS_PER_DAY * 3);
        String batchNo = "th-" + jianxie + dateStr + "-" + seqNum;
        return batchNo;
    }


}
