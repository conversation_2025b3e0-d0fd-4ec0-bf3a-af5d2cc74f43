package com.th.cms.modular.influcer.bizInflucerPlatform.controller;

import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.modular.sysmesage.third.BizInflucerPlatformMessageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("user/influcer-platform-api")
public class BizInflucerPlatformWebController {
    @Autowired
    private BizInflucerPlatformMessageHelper bizInflucerPlatformMessageHelper;

    /**
     * 新分配达人消息/达人发送新消息
     * 对应官方客服
     * 短信、站内信
     * 标题：🔔《客服工作台》消息提醒 - 新会话消息通知<br>内容：您有{3}条未读会话消息 客户「{用户昵称}」发起咨询 【{最新消息摘要}...】 ▶ 立即处理：&lt;查看会话详情&gt;。<br>跳转：跳转到工作区
     *
     * @param bizImLkId 参数
     */
    @RequestMapping("/new_message_sent_by_expert")
    public ResponseData<Boolean> new_message_sent_by_expert(@RequestParam("bizImLkId") Long bizImLkId) {
        bizInflucerPlatformMessageHelper.new_message_sent_by_expert(bizImLkId);
        return ResponseData.success(true);
    }

    /**
     * im消息 对应客服<br>对应商务
     * <div>标题：🔔《客服工作台》消息提醒 - 新会话消息通知<br></div><div>内容：【新消息】客户「{用户昵称}」于[XX:XX]发起咨询，请及时处理！点击&lt;查看详情&gt;跳转会话工作区。</div>
     *
     * @param bizCustomerId 参数
     * @param bizImRecordId 参数
     */
    @RequestMapping("/im_message")
    public ResponseData<Boolean> im_message(@RequestParam("bizCustomerId") Long bizCustomerId, @RequestParam("bizImRecordId") Long bizImRecordId) {
        bizInflucerPlatformMessageHelper.im_message(bizCustomerId, bizImRecordId);
        return ResponseData.success(true);
    }


    /**
     * 提现
     *
     * @param bizInflucerId 达人
     */
    @RequestMapping("/expert_verification_submit")
    public ResponseData<Boolean> expert_verification_submit(@RequestParam("bizInflucerId") Long bizInflucerId) {
        bizInflucerPlatformMessageHelper.expert_verification_submit(bizInflucerId);
        return ResponseData.success(true);
    }

    /**
     * 提现
     *
     * @param bizInflucerId 达人
     */
    @RequestMapping("/tixian")
    public ResponseData<Boolean> tixian(@RequestParam("bizInflucerId") Long bizInflucerId, @RequestParam("accountLogId") Long accountLogId) {
        bizInflucerPlatformMessageHelper.tixian(bizInflucerId, accountLogId);
        return ResponseData.success(true);
    }

    @RequestMapping("/sendNewUserMessage")
    public ResponseData<Boolean> sendNewUserMessage(@RequestParam("bizInflucerId") Long bizInflucerId) {
        bizInflucerPlatformMessageHelper.sendNewUserMessage(bizInflucerId);
        return ResponseData.success(true);
    }


}
