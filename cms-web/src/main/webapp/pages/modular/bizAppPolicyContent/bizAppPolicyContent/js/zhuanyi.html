<link href="${ctxPath}/pages/modular/bizAppPolicyContent/bizAppPolicyContent/js/style.css" rel="stylesheet"/>
<script type="text/javascript" src="${ctxPath}/pages/modular/bizAppPolicyContent/bizAppPolicyContent/js/wangeditor.js"></script>
<style>
    #editor—wrapper {
        border: 1px solid #ccc;
        z-index: 100; /* 按需定义 */
    }

    #toolbar-container {
        border-bottom: 1px solid #ccc;
    }

    #editor-container {
        height: 500px;
    }
</style>
<script>
    function unescapeHTML(str) {
        return str.replace(/&amp;|&lt;|&gt;|&quot;|&#39;/g, (match) => {
            switch (match) {
                case '&amp;': return '&';
                case '&lt;':  return '<';
                case '&gt;':  return '>';
                case '&quot;': return '"';
                case '&#39;':  return "'";
                default:      return match;
            }
        });
    }

    function escapeHTML(str) {
        return str.replace(/[&<>"']/g, (match) => {
            switch (match) {
                case '&': return '&amp;';
                case '<': return '&lt;';
                case '>': return '&gt;';
                case '"': return '&quot;';
                case "'": return '&#39;';
                default: return match;
            }
        })
    }
    const { createEditor, createToolbar } = window.wangEditor

    const editorConfig = {
        placeholder: 'Type here...',
        onChange(editor) {
            const html = editor.getHtml()
            console.log('editor content', html)
            // 也可以同步到 <textarea>
        },
        menu_conf: {
            uploadimage: {
                server: '${ctxPath}/oss/ik/upload',    // 后端接口地址
                fieldname: 'file',         // 上传参数名
                maxfilesize: 5 * 1024 * 1024, // 限制5mb
                allowedfiletypes: ['image/jpeg', 'image/png'], // 允许类型
                withcredentials: true      // 携带cookie
            }
        }
    }

    const editor = createEditor({
        selector: '#editor-container',
        html: '<p><br></p>',
        config: editorConfig,
        mode: 'default', // or 'simple'
    })

    const toolbarConfig = {}

    const toolbar = createToolbar({
        editor,
        selector: '#toolbar-container',
        config: toolbarConfig,
        mode: 'default', // or 'simple'
    })

</script>

