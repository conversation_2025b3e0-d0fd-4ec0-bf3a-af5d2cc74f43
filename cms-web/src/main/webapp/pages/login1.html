<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>明月号系统管理后台登录</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/layui/2.8.3/css/layui.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/layui/2.8.3/layui.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #1e5799 0%, #207cca 25%, #2989d8 50%, #1e5799 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            position: relative;
        }

        /* 动态背景效果 */
        .bg-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            opacity: 0.2;
        }

        /* 登录容器 */
        .login-container {
            width: 90%;
            max-width: 420px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            position: relative;
            z-index: 10;
            backdrop-filter: blur(10px);
            transition: transform 0.4s ease;
        }

        .login-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.35);
        }

        .login-header {
            text-align: center;
            padding: 30px 20px 10px;
            background: linear-gradient(to right, #1e5799, #2989d8);
            color: white;
            position: relative;
        }

        .login-header h2 {
            font-weight: 500;
            letter-spacing: 1px;
            margin-bottom: 5px;
            font-size: 28px;
        }

        .login-icon {
            position: absolute;
            top: 34px;
            left: 16%;
            transform: translateX(-50%);
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .login-icon i {
            font-size: 32px;
            color: #2989d8;
        }

        .system-title {
            color: rgba(255, 255, 255, 0.9);
            font-size: 18px;
            letter-spacing: 2px;
        }

        .login-form {
            padding: 25px 30px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .form-label i {
            margin-right: 8px;
            color: #1e5799;
        }

        .form-input {
            width: 100%;
            height: 48px;
            padding: 0 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s;
            background-color: #f8fbfd;
        }

        .form-input:focus {
            border-color: #2989d8;
            box-shadow: 0 0 10px rgba(41, 137, 216, 0.2);
            outline: none;
        }

        .verification-container {
            display: flex;
            gap: 10px;
        }

        .verification-container .form-input {
            flex: 1;
        }

        .get-code-btn {
            height: 48px;
            min-width: 120px;
            background: linear-gradient(to right, #2989d8, #1e5799);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 10px rgba(41, 137, 216, 0.3);
        }

        .get-code-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(41, 137, 216, 0.4);
        }

        .get-code-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .login-btn {
            width: 100%;
            height: 52px;
            background: linear-gradient(to right, #1e5799, #2989d8);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(41, 137, 216, 0.4);
            letter-spacing: 1px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(41, 137, 216, 0.5);
        }

        .login-footer {
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
            background: #f8f9fa;
        }

        .additional-options {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            font-size: 14px;
        }

        .option-link {
            color: #2989d8;
            text-decoration: none;
            transition: all 0.3s;
        }

        .option-link:hover {
            color: #1e5799;
            text-decoration: underline;
        }

        @media (max-width: 480px) {
            .login-container {
                max-width: 95%;
                margin: 0 auto;
            }

            .login-form {
                padding: 20px;
            }

            .form-input, .get-code-btn {
                height: 44px;
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .floating-element {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            z-index: 0;
            animation: float 8s infinite ease-in-out;
        }

        .element-1 {
            width: 150px;
            height: 150px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .element-2 {
            width: 80px;
            height: 80px;
            top: 60%;
            left: 85%;
            animation-delay: 2s;
        }

        .element-3 {
            width: 120px;
            height: 120px;
            top: 75%;
            left: 15%;
            animation-delay: 4s;
        }

        .element-4 {
            width: 60px;
            height: 60px;
            top: 25%;
            left: 75%;
            animation-delay: 6s;
        }

        .error-message {
            color: #e74c3c;
            font-size: 13px;
            margin-top: 5px;
            height: 20px;
        }
    </style>
</head>
<body>
<!-- 动态背景元素 -->
<div class="bg-particles">
    <div class="floating-element element-1"></div>
    <div class="floating-element element-2"></div>
    <div class="floating-element element-3"></div>
    <div class="floating-element element-4"></div>
</div>

<!-- 登录容器 -->
<div class="login-container">
    <div class="login-header">
        <div class="login-icon">
            <i class="layui-icon layui-icon-password"></i>
        </div>
        <div>
            <h2>账号登录</h2>
            <div class="system-title">明月号系统管理后台</div>
        </div>
    </div>

    <div class="login-form">
        <div class="form-group">
            <label class="form-label">
                <i class="layui-icon layui-icon-username"></i> 手机号码
            </label>
            <input type="tel" id="account" class="form-input" placeholder="请输入11位手机号码">
            <div class="error-message" id="account-error"></div>
        </div>

        <div class="form-group">
            <label class="form-label">
                <i class="layui-icon layui-icon-vercode"></i> 验证码
            </label>
            <div class="verification-container">
                <input type="text" id="captcha" class="form-input" placeholder="请输入验证码">
                <button type="button" id="getCodeBtn" class="get-code-btn">获取验证码</button>
            </div>
            <div class="error-message" id="captcha-error"></div>
        </div>

        <button type="button" id="loginBtn" class="login-btn">登 录</button>

        <!-- <div class="additional-options">
            <a href="#" class="option-link"><i class="layui-icon layui-icon-login-wechat"></i> 微信登录</a>
            <a href="#" class="option-link"><i class="layui-icon layui-icon-util"></i> 忘记密码?</a>
        </div> -->
    </div>

    <div class="login-footer">
        ©2023 明月号系统管理后台 版权所有 | 技术支持: ************
    </div>
</div>

<script type="text/javascript">
    var Feng = {
        ctxPath: "",
        addCtx: function (ctx) {
            if (this.ctxPath === "") {
                this.ctxPath = ctx;
            }
        }
    };
    Feng.addCtx("${ctxPath}");
</script>
<script>

    $(document).ready(function () {
        // 手机号格式验证函数
        function isValidPhone(phone) {
            const reg = /^1[3-9]\d{9}$/;
            return reg.test(phone);
        }


        // 获取验证码按钮点击事件
        $("#getCodeBtn").click(function () {
            const account = $("#account").val().trim();
            const accountError = $("#account-error");

            accountError.text("");

            // 手机号格式验证
            if (!account) {
                accountError.text("请输入手机号码");
                shakeInput("#account");
                return;
            }

            if (!isValidPhone(account)) {
                accountError.text("请输入正确的11位手机号码");
                shakeInput("#account");
                return;
            }

            $.ajax({
                url: Feng.ctxPath + '/send?phone=' + account,
                type: 'GET',
                success: function (res) {

                    if (res.code === 200) {

                        // 模拟发送验证码
                        layer.msg('验证码已发送至您的手机', {icon: 1, time: 2000});

                        // 禁用按钮并开始倒计时
                        const $btn = $(this);
                        $btn.prop("disabled", true);
                        let count = 60;
                        $btn.text(count + "秒后重发");

                        const timer = setInterval(() => {
                            count--;
                            $btn.text(count + "秒后重发");

                            if (count <= 0) {
                                clearInterval(timer);
                                $btn.text("获取验证码").prop("disabled", false);
                            }
                        }, 1000);

                    }
                },
                error: function (err) {
                    layer.msg('回收失败', {icon: 2});
                }
            });
        });

        // 登录按钮点击事件
        $("#loginBtn").click(function () {
            const account = $("#account").val().trim();
            const captcha = $("#captcha").val().trim();
            const accountError = $("#account-error");
            const captchaError = $("#captcha-error");

            accountError.text("");
            captchaError.text("");

            // 验证账号（手机号）
            if (!account) {
                accountError.text("请输入手机号码");
                shakeInput("#account");
                return;
            }

            if (!isValidPhone(account)) {
                accountError.text("请输入正确的11位手机号码");
                shakeInput("#account");
                return;
            }

            // 验证验证码
            if (!captcha) {
                captchaError.text("请输入验证码");
                shakeInput("#captcha");
                return;
            }

            if (captcha.length < 4 || captcha.length > 6) {
                captchaError.text("验证码格式不正确");
                shakeInput("#captcha");
                return;
            }

            // 模拟登录请求
            const $btn = $(this);
            const originalText = $btn.text();
            $btn.text("登录中...").prop("disabled", true);

            setTimeout(() => {
                layer.msg('登录成功！正在进入系统...', {icon: 1, time: 1500}, function () {
                    // 登录成功后跳转
                    window.location.href = "dashboard.html";
                });
            }, 1500);
        });

        // 输入错误时抖动效果
        function shakeInput(selector) {
            $(selector).css("border-color", "#e74c3c");
            $(selector).addClass("shake");
            setTimeout(() => {
                $(selector).removeClass("shake");
            }, 500);
        }

        // 手机号输入框自动格式化
        $("#account").on("input", function () {
            let value = $(this).val().replace(/\D/g, "");
            if (value.length > 11) {
                value = value.substring(0, 11);
            }
            $(this).val(value);

            if (isValidPhone(value)) {
                $(this).css("border-color", "#2ecc71");
            } else {
                $(this).css("border-color", "");
            }
        });

        // 验证码输入框限制
        $("#captcha").on("input", function () {
            $(this).val($(this).val().replace(/\D/g, ""));
        });
    });
</script>
<style>
    /* 抖动动画效果 */
    .shake {
        animation: shake 0.5s cubic-bezier(.36, .07, .19, .97) both;
    }

    @keyframes shake {
        10%, 90% {
            transform: translateX(-1px);
        }
        20%, 80% {
            transform: translateX(2px);
        }
        30%, 50%, 70% {
            transform: translateX(-4px);
        }
        40%, 60% {
            transform: translateX(4px);
        }
    }
</style>
</body>
</html>