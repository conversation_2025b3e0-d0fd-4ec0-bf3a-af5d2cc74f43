package com.th.cms.modular.bizClue.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 达人质检测表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizClueUpload implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 审核ID
     */
    private Integer arraignId;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 外站账号
     */
    private String account;

    /**
     * 外站ID
     */
    private String accountId;

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 分组id
     */
    private Integer areaId;

    /**
     * 审核人id
     */
    private Long checkId;

    /**
     * 质检领取人id
     */
    private Long receiveId;

    /**
     * 领取线索表id
     */
    private Integer clueReceiveId;

    /**
     * 上传商务
     */
    private Long ownerStaffId;

    /**
     * 上传商务名称
     */
    private String ownerStaffName;

    /**
     * 商务分组id
     */
    private Long ownerStaffGroupId;

    /**
     * 微信号
     */
    private String wxNum;

    /**
     * 来源平台
     */
    private String platform;

    /**
     * 可扩展平台
     */
    private String extendPlatform;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 外站主页链接
     */
    private String url;

    /**
     * 上传文件
     */
    private String file;

    /**
     * 审核状态:1=待审核,2=已通过,3=已驳回,4=已修改待审核
     */
    private String status;

    /**
     * 是否离职:0=正常,1=离职
     */
    private Integer isQuit;

    /**
     * 离职时间
     */
    private LocalDateTime quitTime;

    /**
     * 是否释放:0=正常,1=释放
     */
    private Integer isRelease;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 审核时间
     */
    private LocalDateTime checkTime;

    /**
     * 释放时间
     */
    private LocalDateTime releaseTime;

    /**
     * 是否分配 0->否 1->是
     */
    private Integer isDistribution;

    /**
     * 分配时间
     */
    private LocalDateTime distributionTime;

    /**
     * 分配人id
     */
    private Integer distributionId;

    /**
     * 获取人id
     */
    private Integer getUserId;

    /**
     * 是否上传 1=自己上传,2=领取线索上传
     */
    private Integer isUpload;

    /**
     * 是否删除:0未删除=1=已删除
     */
    private Integer isDelete;

    /**
     * 达人类型： 1=直播达人 ，2=视频达人，3=商家达人
     */
    private Integer leadsType;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 商务分组名称
     */
    private String ownerStaffGroupName;

    /**
     * 达人头像
     */
    private String avatar;
}
