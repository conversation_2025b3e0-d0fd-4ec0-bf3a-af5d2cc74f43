<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 定义应用名称，用于日志文件名 -->
    <property name="APP_NAME" value="myh-cms-web"/>
    <!-- 定义日志输出路径 -->
    <property name="LOG_PATH" value="./logs"/>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                %date [%thread] %X{X-B3-TraceId:-} %X{X-B3-SpanId:-} %X{X-Span-Export:-} %-5level %logger{50}:%L - %msg%n
            </pattern>
        </encoder>
    </appender>

    <!-- 文件输出（按天滚动，保留7天） -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 每天生成一个日志文件 -->
            <fileNamePattern>${LOG_PATH}/${APP_NAME}-%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 保留最近7天的日志 -->
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>
                %date [%thread] %X{X-B3-TraceId:-} %X{X-B3-SpanId:-} %X{X-Span-Export:-} %-5level %logger{50}:%L - %msg%n
            </pattern>
        </encoder>
    </appender>
    <logger name="com.th.cms">
        <level value="INFO"/>
    </logger>
    <logger name="org.mybatis.spring">
        <level value="INFO"/>
    </logger>
    <logger name="Mybatis2Sql">
        <level value="DEBUG"/>
    </logger>
    <!-- 日志输出级别设置 -->
    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>

</configuration>