package com.th.cms.modular.middleStation.service;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.th.cms.core.util.JedisClient;
import com.th.cms.modular.bizClue.entity.BizClue;
import com.th.cms.modular.bizClue.service.BizClueService;
import com.th.cms.modular.middleStation.model.ClueInfoParam;
import com.th.cms.modular.middleStation.model.MidStationClueCreateReq;
import com.th.cms.modular.middleStation.model.MidStationUser;
import com.th.cms.modular.middleStation.model.RemarkVO;
import com.th.cms.modular.system.entity.User;
import com.th.cms.modular.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class MidStationInteractionService {

    @Resource
    private UserService userService;

    @Value("${midstation.host:http://************:10007}")
    private String midStationHost;

    @Resource
    private JedisClient jedisClient;

    @Resource
    private BizClueService bizClueService;

    /** 中台用户存入缓存的key*/
    private static final String MID_STATION_USER_KEY_PREFIX = "mid_station_user-";

    public RemarkVO getClueId(ClueInfoParam clueInfoParam){
        User user = userService.getUserByContext();
        String s = jedisClient.get(MID_STATION_USER_KEY_PREFIX + user.getUserId());
        MidStationUser midStationUser = null;
        if (StrUtil.isNotBlank(s)){
            String token = getToken(user.getPhone(), "mingyuehao", 4);
            midStationUser = JSONObject.parseObject(s, MidStationUser.class);
            midStationUser.setToken(token);
        }else {
            midStationUser = getUserNoExist(user.getPhone());
            String token = getToken(user.getPhone(), "mingyuehao", 4);
            midStationUser.setToken(token);
            jedisClient.setExpire(MID_STATION_USER_KEY_PREFIX+user.getUserId(),120L, JSONObject.toJSONString(midStationUser));
        }

        Long midStationClueId = getMidStationClueIdIfNotExist(clueInfoParam, midStationUser);

        return RemarkVO.builder().clueId(midStationClueId).token(midStationUser.getToken()).userId(midStationUser.getId()).build();
    }

    /**
     * 获取中台用户token
     * @param userPhone
     * @param app
     * @param loginWay
     * @return
     */
    public String getToken(String userPhone,String app,Integer loginWay){
        String url = midStationHost+"/common-soa/admin/login";
        Map<String,Object> param = new HashMap<>();
        param.put("userAccount",userPhone);
        param.put("app",app);
        param.put("loginWay",loginWay);
        log.info("中台获取token入参:{}",param);
        String body = HttpUtil.createPost(url).body(JSONObject.toJSONString(param),ContentType.JSON.getValue()).execute().body();
        log.info("中台获取token响应:{}",body);
        Assert.notBlank(body,"请求token获取接口异常");
        JSONObject jsonObject = JSONObject.parseObject(body);
        Assert.isTrue(StrUtil.equals("200",jsonObject.getString("code")),jsonObject.getString("message"));
        return jsonObject.getString("data");
    }


    /**
     * 获取中台达人线索id，不存在则新增达人线索
     * @param clueInfoParam
     * @return
     */
    public Long getMidStationClueIdIfNotExist(ClueInfoParam clueInfoParam,MidStationUser midStationUser){
        BizClue bizClue = bizClueService.getById(clueInfoParam.getClueId());
        Long midStationClueId = getMidStationClueId(bizClue.getUrl());
        if (Objects.isNull(midStationClueId)){
            log.info("明月号线索id:{},主页地址=={}==对应中台线索为空",bizClue.getId(),bizClue.getUrl());
            midStationClueId = createMidStationClue(bizClue,midStationUser);
            log.info("创建中台线索成功，中台线索id为：{}",bizClue.getId());
        }
        return midStationClueId;
    }

    /**
     * 获取中台达人线索
     * @param homeUrl
     * @return
     */
    public Long getMidStationClueId(String homeUrl){
        String url = midStationHost+"/common-soa/experter/getZtExporterId";
        Map<String,Object> param = new HashMap<>();
        param.put("homeUrl",homeUrl);
        param.put("app","mingyuehao");
        log.info("中台线索获取入参:{}",param);
        String body = HttpUtil.createPost(url).body(JSONObject.toJSONString(param),ContentType.JSON.getValue()).execute().body();
        log.info("中台线索获取响应:{}",body);
        JSONObject jsonObject = JSONObject.parseObject(body);
        Assert.isTrue(StrUtil.equals("200",jsonObject.getString("code")),jsonObject.getString("message"));
        return jsonObject.getLong("data");
    }

    private Long createMidStationClue(BizClue bizClue,MidStationUser midStationUser){
        MidStationClueCreateReq req = new MidStationClueCreateReq();
        req.setApp("mingyuehao");
        req.setCreateUserid(midStationUser.getId());
        req.setHomeUrl(bizClue.getUrl());
        req.setLabelTypeIds("1");
        req.setNickName(bizClue.getNickname());
        req.setOutId(bizClue.getAccountId());
        req.setOutPlatform(bizClue.getPlatform());
        req.setRemark("");

        String url = midStationHost+"/common-soa/experter/addZtExperter";
        log.info("中台线索添加入参:{}",req);
        HttpRequest post = HttpUtil.createPost(url);
        String response = post.body(JSONObject.toJSONString(req), ContentType.JSON.getValue()).execute().body();
        log.info("中台线索添加响应:{}",response);
        JSONObject jsonObject = JSONObject.parseObject(response);
        Assert.isTrue(Objects.nonNull(jsonObject) && StrUtil.equals("200",jsonObject.getString("code")),jsonObject.getString("message"));
        return jsonObject.getLong("data");
    }

    /**
     * 创建中台用户
     * @param username
     * @param mobile
     * @param app
     * @param operator
     */
    public Long createUser(String username,String mobile,String app,String operator){
        String url = midStationHost+"/common-soa/admin/submitUser";
        Map<String,Object> param = new HashMap<>();
        param.put("username",username);
        param.put("mobile",mobile);
        param.put("ztAppCodes", Arrays.asList(app));
        param.put("operator",operator);
        log.info("中台用户创建入参:{}",param);
        String body = HttpUtil.createPost(url).body(JSONObject.toJSONString(param),ContentType.JSON.getValue()).execute().body();
        log.info("中台用户创建响应:{}",body);
        Assert.notBlank(body,"请求创建用户接口异常");
        JSONObject jsonObject = JSONObject.parseObject(body);
        Assert.isTrue(StrUtil.equals("200",jsonObject.getString("code")),jsonObject.getString("message"));
        JSONObject data = jsonObject.getJSONObject("data");
        Assert.isTrue(Objects.nonNull(data)&& Objects.nonNull(data.get("id")),"创建失败!");
        return data.getLong("id");
    }


    /**
     * 获取中台用户，不存在则创建
     * @param phone
     * @return
     */
    public MidStationUser getUserNoExist(String phone){
        MidStationUser user = getUser(phone);
        if(user == null){
            User currentUser = userService.getUserByContext();
            Long midStationUserId = createUser(currentUser.getName(), currentUser.getPhone(), "mingyuehao", "0");
            user.setId(midStationUserId);
            user.setName(currentUser.getName());
            user.setPhone(currentUser.getPhone());
        }
        log.info("获取到的中台用户:{}",user);
        return user;
    }

    /**
     * 获取中台用户
     * @param userPhone
     * @return
     */
    public MidStationUser getUser(String userPhone){
        Assert.notBlank(userPhone,"查询用户手机号为空!");
        String url = midStationHost+"/common-soa/admin/getUserInfo";
        Map<String,Object> param = new HashMap<>();
        param.put("mobile",userPhone);
        log.info("中台用户查询入参:{}",param);
        String body = HttpUtil.get(url,param);
        log.info("中台用户查询响应:{}",body);
        Assert.notBlank(body,"获取账户信息接口异常");
        JSONObject jsonObject = JSONObject.parseObject(body);
        if (StrUtil.equals("40000",jsonObject.getString("code")) && "账号不存在".equals(jsonObject.getString("message"))){
            return null;
        }
        Assert.isTrue(StrUtil.equals("200",jsonObject.getString("code")),jsonObject.getString("message"));
        MidStationUser midStationUser = jsonObject.getJSONObject("data").toJavaObject(MidStationUser.class);
        Assert.notNull(midStationUser.getId(),"获取中台用户ID为空!");
        midStationUser.setPhone(userPhone);
        return midStationUser;
    }

}
