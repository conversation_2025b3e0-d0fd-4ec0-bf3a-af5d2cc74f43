package com.th.cms.modular.sysmesage.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 消息模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysMessageTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模板标题
     */
    private String title;
    /**
     * 模板名称
     */
    private String name;
    /**
     * 模板内容
     */
    private String content;

    /**
     * 带有变量的模板内容
     */
    private String dataContent;

    /**
     * 消息类型：对应sys_message_tag表中字典
     */
    private Integer msgType;

    /**
     * 消息使用场景
     */
    private String msgUseWhere;

    /**
     * 消息对应人群
     */
    private String msgPerson;

    /**
     * 发送时机
     */
    private String sendOccasion;

    /**
     * 发送类型 短信 站内信 apppush
     */
    private Integer sendType;

    /**
     * 模板代码
     */
    private String templateCode;

    /**
     * 供应商代码
     */
    private Integer supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态 1 启用 0 不可用
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新人ID
     */
    private Long updateId;

    /**
     * 最后更新人姓名
     */
    private String updateName;

    /**
     * 最后更新时间
     */
    private Date updateTime;

}
