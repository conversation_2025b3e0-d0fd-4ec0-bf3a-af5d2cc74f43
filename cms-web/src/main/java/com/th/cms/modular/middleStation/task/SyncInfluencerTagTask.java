package com.th.cms.modular.middleStation.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.th.cms.modular.bizClue.service.BizClueTagService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class SyncInfluencerTagTask {

    @Resource
    private BizClueTagService bizClueTagService;


    @Scheduled(cron = "0 0/10 * * * ? ")
    public void syncInfluencerTag() {
        Date now = new Date();
        DateTime pullTime = DateUtil.offsetDay(now, -1);
        Date startTime = DateUtil.beginOfDay(pullTime);
        Date endTime = DateUtil.endOfDay(pullTime);
        bizClueTagService.syncTags(startTime, endTime);
    }
}
