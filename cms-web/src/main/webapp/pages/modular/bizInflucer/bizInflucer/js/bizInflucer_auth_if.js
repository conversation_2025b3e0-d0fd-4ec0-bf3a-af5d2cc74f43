layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;

    //列表iframe
    var iframe = $("#bizInflucerAuthIframe");
    var baseUrl = "http://api.mingyuehao.com/approval/#/expertCertification/add";
    //var baseUrl = "http://192.168.4.124:3000/approval/#/expertCertification/add";

    var token = $("#tokenId").val();
    var mobile = Feng.getUrlParam("mobile");
    if (mobile) {
        var params = {
            authToken: token,
            mobile: mobile
        };
        iframe.attr("src", baseUrl + "?" + $.param(params));
    }else{
        var params = {
            authToken: token
        };

        iframe.attr("src", baseUrl + "?" + $.param(params));
    }
});
