@layout("/common/_container.html",{js:["/pages/modular/bizAppPolicyContent/bizAppPolicyContent/js/bizAppPolicyContent_detail.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">政策内容管理详情</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizAppPolicyContentForm" lay-filter="bizAppPolicyContentForm" class="layui-form model-form" >
                <input id="id" name="id" type="hidden"/>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">分类</label>
                        <div class="layui-input-inline">
                            <select id="policyId" name="policyId" lay-filter="questType" lay-search=""></select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">平台</label>
                        <div class="layui-input-inline">
                            <select id="platformId" name="platformId" lay-filter="questType" lay-search="">
                            </select>
                        </div>
                    </div>

                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">标题</label>
                        <div class="layui-input-inline">
                            <input id="title" name="title" placeholder="请输入title" type="text" class="layui-input"
                                   lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status" id="status" lay-filter="status">
                                <option value=""></option>
                                <option value="1">启用</option>
                                <option value="-1">禁用</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">热点值</label>
                        <div class="layui-input-inline">
                            <input id="hotNum" name="hotNum" placeholder="请输入热点值" type="number"
                                   class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">排序字段</label>
                        <div class="layui-input-inline">
                            <input id="sortNum" name="sortNum" placeholder="请输入sortNum" type="text"  class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">图片</label>
                    <div class="layui-input-inline">
                        <div class="layui-upload">
                            <div class="layui-upload-list">
                                <img class="layui-upload-img" style="width: 200px;height: 200px" id="imgPicImg">
                                <input type="hidden" name="imgPic" id="imgPicHidden">
                                <p id="demoText"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">内容</label>
                    <div class="layui-input-block">
                        <div id="editor—wrapper">
                            <div id="toolbar-container"><!-- 工具栏 --></div>
                            <div id="editor-container"><!-- 编辑器 --></div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-item  text-center">
                        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog"
                                id="backupPage">返回
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@ include("/modular/bizAppPolicyContent/bizAppPolicyContent/js/zhuanyi.html"){}
@}
