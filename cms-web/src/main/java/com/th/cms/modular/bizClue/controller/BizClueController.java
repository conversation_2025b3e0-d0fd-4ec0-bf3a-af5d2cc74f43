package com.th.cms.modular.bizClue.controller;


import cn.hutool.core.date.DateUtil;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.modular.bizClue.service.BizClueTagService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;


/**
 * <p>
 * 线索池 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Controller
@RequestMapping("/bizClue")
public class BizClueController extends BaseController {

    @Resource
    BizClueTagService bizClueTagService;

    private static final String PREFIX = "/modular/bizClue/";

    @RequestMapping("")
    public String index(){
        return PREFIX + "bizClue.html";
    }



    @RequestMapping("/sync-tags")
    @ResponseBody
    public ResponseData syncTags(@RequestParam String startTime,@RequestParam String endTime){
        bizClueTagService.syncTags(DateUtil.parse(startTime), DateUtil.parse(endTime));
        return ResponseData.success();
    }
}
