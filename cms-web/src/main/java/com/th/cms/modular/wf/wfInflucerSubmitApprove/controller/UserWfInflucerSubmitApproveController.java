package com.th.cms.modular.wf.wfInflucerSubmitApprove.controller;

import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.easyexcel.EasyExcelExportService;
import com.th.cms.core.easyexcel.PageResult;
import com.th.cms.core.util.DateUtils;
import com.th.cms.core.util.PathUtil;
import com.th.cms.modular.enums.ProjectStatus;
import com.th.cms.modular.influcer.bizInflucer.model.enums.PlatformType;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatformVO;
import com.th.cms.modular.influcer.bizPlatform.service.BizPlatformService;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.settle.settleProjects.service.SettleProjectsService;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.*;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.enums.InflucerType;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.reqparam.WfInflucerSubmitApproveListParam;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.service.WfApproveStatusService;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.service.WfInflucerSubmitApproveService;
import com.th.cms.modular.wf.wfStep.model.StepNodeType;
import com.th.cms.modular.wf.wfStep.model.WfStep;
import com.th.cms.modular.wf.wfStep.model.WfStepConfig;
import com.th.cms.modular.wf.wfStep.service.WfStepConfigService;
import com.th.cms.modular.wf.wfStep.service.WfStepService;
import com.th.cms.modular.wf.wfType.model.StepTypeVO;
import com.th.cms.util.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static com.th.cms.modular.oss.service.AliUploadOssImg.uploadImg;

/**
 * wfInflucerSubmitApprove控制器
 *
 * <AUTHOR>
 * @Date 2025-05-27 14:53:59
 */
@Controller
@RequestMapping("/user/submitApprove")
public class UserWfInflucerSubmitApproveController extends BaseController {

    @Autowired
    private WfInflucerSubmitApproveService wfInflucerSubmitApproveService;
    @Resource
    private SettleProjectsService settleProjectsService;
    @Resource
    private BizPlatformService bizPlatformService;
    @Autowired
    private WfStepService wfStepService;
    @Autowired
    private WfStepConfigService wfStepConfigService;
    @Autowired
    private WfApproveStatusService wfApproveStatusService;
    @Autowired
    private UserUtil userUtil;
    @Autowired
    private EasyExcelExportService easyExcelExportService;


    /**
     * 获取wfInflucerSubmitApprove列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(WfInflucerSubmitApproveListParam wfInflucerSubmitApproveParam) {
        return wfInflucerSubmitApproveService.findPageBySpec(wfInflucerSubmitApproveParam);
    }

    /**
     * 达人类型
     *
     * @return
     */
    @GetMapping(value = "/influcerType")
    @ResponseBody
    public ResponseData influcerType() {

        List<StepTypeVO> vos = new ArrayList<>();
        InflucerType[] values = InflucerType.values();
        for (InflucerType value : values) {
            vos.add(StepTypeVO.builder().name(value.name).value(value.value).build());
        }

        return ResponseData.success(vos);
    }

    /**
     * 外站平台
     * 取认证平台
     *
     * @return
     */
    @GetMapping(value = "/outPlatform")
    @ResponseBody
    public ResponseData outPlatform() {

        List<BizPlatformVO> platformVOS = new ArrayList<>();
        for (PlatformType value : PlatformType.values()) {
            platformVOS.add(BizPlatformVO.builder()
                    .id(String.valueOf(value.id))
                    .platName(value.name)
                    .iconPic(value.image)
                    .jiancheng(value.name)
                    .build());
        }

        return ResponseData.success(platformVOS);
    }


    /**
     * 合作平台
     * 二、三级平台列表
     *
     * @return
     */
    @GetMapping(value = "/platformList")
    @ResponseBody
    public ResponseData platformList() {

        QueryWrapper<BizPlatform> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().gt(BizPlatform::getLevels, 1);
        List<BizPlatform> list = bizPlatformService.list(queryWrapper);
        return ResponseData.success(list);
    }

    /**
     * 根据合作平台查询项目列表
     *
     * @param platformId
     * @return
     */
    @GetMapping(value = "/projectList")
    @ResponseBody
    public ResponseData projectList(@RequestParam(required = false) Long platformId,
                                    @RequestParam(required = false, value = "projectName") Long projectName) {

        QueryWrapper<SettleProjects> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .select(SettleProjects::getId, SettleProjects::getProjectName,
                        SettleProjects::getPlatformId, SettleProjects::getPlatform,
                        SettleProjects::getFlowId)
                .eq(null != platformId, SettleProjects::getPlatformId, platformId)
                .like(!StringUtils.isEmpty(projectName), SettleProjects::getProjectName, "%" + projectName + "%")
                .gt(SettleProjects::getFlowId, 0)
                .eq(SettleProjects::getProjectStatus, ProjectStatus.APPROVED.value);
        List<SettleProjects> list = settleProjectsService.list(queryWrapper);
        return ResponseData.success(list);
    }

    /**
     * 根据合作平台查询项目列表
     *
     * @return
     */
    @GetMapping(value = "/statusList")
    @ResponseBody
    public ResponseData<List<WfApproveStatus>> statusList() {
        List<WfApproveStatus> list = wfApproveStatusService.list();
        return ResponseData.success(list);
    }

    /**
     * 提审表单页配置
     *
     * @param flowId
     * @return
     */
    @GetMapping(value = "/submitForm")
    @ResponseBody
    public ResponseData submitForm(@RequestParam Long flowId) {

        if (flowId != null) {

            QueryWrapper<WfStep> stepQueryWrapper = new QueryWrapper<>();
            stepQueryWrapper.lambda().eq(WfStep::getTypeId, flowId).eq(WfStep::getStepType, StepNodeType.StartNode.getCode());
            List<WfStep> startSteps = wfStepService.list(stepQueryWrapper);

            if (!CollectionUtils.isEmpty(startSteps)) {

                QueryWrapper<WfStepConfig> configQueryWrapper = new QueryWrapper<>();
                configQueryWrapper.lambda().eq(WfStepConfig::getStepId, startSteps.get(0).getId());
                WfStepConfig stepConfig = wfStepConfigService.getOne(configQueryWrapper);
                return ResponseData.success(stepConfig);
            }
        }

        return ResponseData.success();
    }

    /**
     * 提审操作
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/submit")
    @ResponseBody
    public ResponseData submitApprove(@RequestBody UploadSubmitApproveDTO dto) throws IllegalAccessException {
        String message = wfInflucerSubmitApproveService.submitApprove(dto);
        if (!StringUtils.isEmpty(message)) {
            return ResponseData.error(message);
        }
        return ResponseData.success();
    }

    /**
     * 提审详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/detail")
    @ResponseBody
    public ResponseData approveDetail(@RequestParam Long id) {

        SubmitApproveVO submitApproveVO = wfInflucerSubmitApproveService.approveDetail(id);

        if (!StringUtils.isEmpty(submitApproveVO.getMessage())) {
            return ResponseData.error(submitApproveVO.getMessage());
        }

        return ResponseData.success(submitApproveVO);
    }

    @DeleteMapping(value = "/delete")
    @ResponseBody
    public ResponseData approveDelete(@RequestParam Long id) {
        return ResponseData.success(wfInflucerSubmitApproveService.removeById(id));
    }

    /**
     * 审核
     * 提审单（WfInflucerSubmitApprove）
     * 审批单（WfApprovalRequest）
     * 审批记录（WfApprovalRecord）
     */
    @PostMapping(value = "/approve")
    @ResponseBody
    public ResponseData approve(@RequestBody SubmitApproveRequestDTO dto) {
        String message = wfInflucerSubmitApproveService.approve(dto);
        if (!StringUtils.isEmpty(message)) {
            return ResponseData.error(message);
        }
        return ResponseData.success();
    }

    /**
     * 绑定、运营绑定操作-不是审核-
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/operateRecord")
    @ResponseBody
    public ResponseData operateRecord(@RequestBody OperateRecordDTO dto) {
        String message = wfInflucerSubmitApproveService.operateRecord(dto);
        if (!StringUtils.isEmpty(message)) {
            return ResponseData.error(message);
        }
        return ResponseData.success();
    }

    /**
     * 驳回指定节点
     *
     * @param wfInflucerSubmitApprove
     * @return
     */
    //TODO 驳回指定节点
    @PostMapping(value = "/revoked")
    @ResponseBody
    public ResponseData revoked(WfInflucerSubmitApprove wfInflucerSubmitApprove) {

        //驳回
//        {
//                  "id": "数据id-详情中取",
//                  "recordId": "记录id-详情中取",
//                  "stepId": "被驳回到节点id",
//                  "approvalComment": "拒绝原因"
//        }

        return ResponseData.success();
    }


    @RequestMapping(value = "/export")
    @ResponseBody
    public ResponseData export(@RequestParam("complete") String complete, HttpServletResponse response) throws IOException {

        List<WfInflucerSubmitApproveExportVO> exportData = wfInflucerSubmitApproveService.getExportData(complete);

        AtomicInteger index = new AtomicInteger(1);
        String dateStr = DateUtils.formatDateForYMDSTR(new Date());
        String fileName = "提报记录_" + dateStr;
        if (!StringUtils.isEmpty(complete) || "1".equals(complete)) {
            fileName = "提审记录_" + dateStr;
        }

        File file = PathUtil.getTmpFile(fileName + ".xlsx");

        file = easyExcelExportService.exportExcelByPage(file, "提审数据", WfInflucerSubmitApproveExportVO.class, t -> convertExcelPageResult(exportData));

        String path = uploadImg(file.getAbsolutePath(), file.getName());
        if (!path.contains("https") && path.contains("http")) {
            return ResponseData.success(path.replace("http", "https"));
        }
        System.out.println(path);
        return ResponseData.success(path);
    }

    public static PageResult<WfInflucerSubmitApproveExportVO> convertExcelPageResult(List<WfInflucerSubmitApproveExportVO> data) {
        PageResult<WfInflucerSubmitApproveExportVO> pageResult = new PageResult();
        pageResult.setPageNum(1);
        pageResult.setPageSize(data.size());
        pageResult.setTotal(data.size());
        pageResult.setTotalPages(1);
        pageResult.setList(data);
        return pageResult;
    }

//    @RequestMapping(value = "/export")
//    public void export(@RequestParam("complete") String complete, HttpServletResponse response) {
//
//        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//        response.setCharacterEncoding("utf-8");
//
//        List<WfInflucerSubmitApproveExportVO> exportData = wfInflucerSubmitApproveService.getExportData(complete);
//
//        try (OutputStream outputStream = response.getOutputStream()) {
//
//            // 1. 获取数据并转换
//            String dateStr = DateUtils.formatDateForYMDSTR(new Date());
//
//            String fileName = URLEncoder.encode("提报记录_" + dateStr, "UTF-8").replaceAll("\\+", "%20");
//            if (!StringUtils.isEmpty(complete) || "1".equals(complete)) {
//                fileName = URLEncoder.encode("提审记录_" + dateStr, "UTF-8").replaceAll("\\+", "%20");
//            }
//
//            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
//
//            // 3. 配置Excel参数
//            WriteCellStyle headStyle = new WriteCellStyle();
//            headStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
//            WriteFont headFont = new WriteFont();
//            headFont.setFontHeightInPoints((short) 12);
//            headStyle.setWriteFont(headFont);
//
//            // 使用 EasyExcel 写入数据
//            EasyExcel.write(outputStream, WfInflucerSubmitApproveExportVO.class)
////                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 替换为自动调整策略
////                    .registerWriteHandler(new SettleStatusValidationHandler(7))
////                    .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, new WriteCellStyle()))
//                    .sheet("提审明细") // 工作表名称
//                    .doWrite(exportData); // dataList 是你要导出的数据列表
//        } catch (IOException e) {
//            // 处理异常
//            throw new RuntimeException("导出失败，请检查服务器状态");
//        }
//
//
////        // 1. 获取数据并转换
////        String dateStr = DateUtils.formatDateForYMDSTR(new Date());
////
////        String fileName = URLEncoder.encode("提报记录_" + dateStr, "UTF-8").replaceAll("\\+", "%20");
////        if (!StringUtils.isEmpty(complete) || "1".equals(complete)) {
////            fileName = URLEncoder.encode("提审记录_" + dateStr, "UTF-8").replaceAll("\\+", "%20");
////        }
////
////        response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
////
////        // 3. 配置Excel参数
////        WriteCellStyle headStyle = new WriteCellStyle();
////        headStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
////        WriteFont headFont = new WriteFont();
////        headFont.setFontHeightInPoints((short) 12);
////        headStyle.setWriteFont(headFont);
////
////        // 4. 执行导出
////        EasyExcel.write(response.getOutputStream(), WfInflucerSubmitApproveExportVO.class)
////                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 替换为自动调整策略
////                .registerWriteHandler(new SettleStatusValidationHandler(7))
////                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, new WriteCellStyle()))
////                .sheet("提审明细")
////                .doWrite(exportData);
//    }
}
