package com.th.cms.modular.wf.wechatContact.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.core.page.PageFactory;
import cn.stylefeng.roses.kernel.model.enums.YesOrNotEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.auth.AuthMybatis;
import com.th.cms.core.dto.FrontPageResult;
import com.th.cms.modular.enums.ApprovalBillType;
import com.th.cms.modular.influcer.bizInflucer.model.enums.PlatformAuthType;
import com.th.cms.modular.influcer.bizInflucer.model.enums.ReceiveType;
import com.th.cms.modular.influcer.bizReceive.model.BizReceive;
import com.th.cms.modular.influcer.bizReceive.service.BizReceiveService;
import com.th.cms.modular.system.entity.Dept;
import com.th.cms.modular.system.entity.User;
import com.th.cms.modular.system.service.UserService;
import com.th.cms.modular.userindex.service.UserIndexService;
import com.th.cms.modular.wf.wechatContact.enums.TaskApproveStatus;
import com.th.cms.modular.wf.wechatContact.enums.TaskReceiveStatus;
import com.th.cms.modular.wf.wechatContact.mapper.InfluencerComWechatMapper;
import com.th.cms.modular.wf.wechatContact.model.InfluencerComWechat;
import com.th.cms.modular.wf.wechatContact.model.req.*;
import com.th.cms.modular.wf.wechatContact.model.vo.TaskReceiveVO;
import com.th.cms.modular.wf.wechatContact.model.vo.WechatContactAuditVO;
import com.th.cms.modular.wf.wfApprovalRecord.model.WfApprovalRecord;
import com.th.cms.modular.wf.wfApprovalRecord.service.WfApprovalRecordService;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.WfInflucerSubmitApprove;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.enums.InflucerType;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.service.WfInflucerSubmitApproveService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WechatContactService extends ServiceImpl<InfluencerComWechatMapper,InfluencerComWechat> implements IService<InfluencerComWechat> {


    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private WfApprovalRecordService wfApprovalRecordService;

    @Resource
    private WfInflucerSubmitApproveService wfInflucerSubmitApproveService;

    @Resource
    private UserService userService;

    private static final String RECEIVE_TASK_KEY = "receive-wechat-contact";

    @Autowired
    private BizReceiveService bizReceiveService;

    @Resource
    private UserIndexService userIndexService;

    /**
     * 创建企微建联记录
     * @param req
     */
    public void wechatContact(WechatContactSaveReq req, User user) {
        WfInflucerSubmitApprove submitApprove = wfInflucerSubmitApproveService.getById(req.getSubmitApproveId());
        Assert.notNull(submitApprove,"提审记录不存在!");

        List<InfluencerComWechat> existRecord = getWechatContactRecord(req.getSubmitApproveId());
        Assert.isTrue(CollectionUtil.isEmpty(existRecord),"当前线索已建联,请勿重复提交!");
        req.setInfluencerId(Objects.isNull(submitApprove.getInfluId()) ? null : submitApprove.getInfluId());
        req.setNickName(submitApprove.getInflucerName());
        req.setPlatformId(submitApprove.getPlatformId());
        req.setPlatformName(submitApprove.getPlatformName());
        req.setProjectId(submitApprove.getProjectId());
        req.setProjectName(submitApprove.getProjectName());
        req.setPhone(submitApprove.getPhone());
        req.setInfluencerType(Integer.parseInt(submitApprove.getInflucerType()));
        req.setCustomerDeptId(user.getDeptId());
        req.setCustomerDeptName(user.getDeptName());
        req.setCustomerId(user.getUserId());
        req.setCustomerName(user.getName());
        req.setCreateId(user.getUserId());
        InfluencerComWechat influencerComWechat = BeanUtil.toBean(req, InfluencerComWechat.class);
        influencerComWechat.setAuditStatus(TaskApproveStatus.SUBMIT.getValue());
        influencerComWechat.setReceiveStatus(TaskReceiveStatus.PENDING.getValue());
        influencerComWechat.setAuthImg(delHttp(influencerComWechat.getAuthImg()));
        influencerComWechat.setAuthVideo(delHttp(influencerComWechat.getAuthVideo()));
        if (req.getAuthImg().contains("?")){
            List<String> split = StrUtil.split(req.getAuthImg(), "?");
            influencerComWechat.setAuthImg(split.get(0));
        }
        if (req.getAuthVideo().contains("?")){
            List<String> split = StrUtil.split(req.getAuthVideo(), "?");
            influencerComWechat.setAuthVideo(split.get(0));
        }
        influencerComWechat.setCreateTime(new Date());
        influencerComWechat.setUpdateTime(new Date());
        wechatContactSaveDB(influencerComWechat,req.getSubmitApproveId());
    }

    @Transactional
    public void wechatContactSaveDB(InfluencerComWechat influencerComWechat,Long submitApproveId){
        this.save(influencerComWechat);
        LambdaUpdateWrapper<WfInflucerSubmitApprove> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(WfInflucerSubmitApprove::getWechatStatus, YesOrNotEnum.Y.getCode());
        updateWrapper.set(WfInflucerSubmitApprove::getWechatStatusName,YesOrNotEnum.Y.getDesc());
        updateWrapper.eq(WfInflucerSubmitApprove::getId, submitApproveId);
        wfInflucerSubmitApproveService.update(updateWrapper);
    }


    /**
     * 审核列表
     * @param req
     * @return
     */
    @AuthMybatis(useRole = true,createUserField = " audit_user_id ")
    public FrontPageResult<WechatContactAuditVO> auditList(WechatContactListReq req){
        IPage<InfluencerComWechat> influencerComWechatIPage = this.baseMapper.auditList(PageFactory.defaultPage(), req);
        IPage<WechatContactAuditVO> resultPage = new Page<>();
        if (Objects.nonNull(influencerComWechatIPage) && 0 < influencerComWechatIPage.getTotal()) {
            BeanUtil.copyProperties(influencerComWechatIPage, resultPage,"records","optimizeCountSql","isSearchCount");
            List<InfluencerComWechat> records = influencerComWechatIPage.getRecords();
            List<WechatContactAuditVO> resultList = entityToVO(records);
            resultPage.setRecords(resultList);
        }
        FrontPageResult<WechatContactAuditVO> frontPageResult = FrontPageResult.convertPage(influencerComWechatIPage,e -> BeanUtil.toBean(e, WechatContactAuditVO.class));
        dataConvert(frontPageResult.getList());
        return frontPageResult;
    }

    /**
     * 个人提交建联列表
     * @param req
     * @return
     */
    public FrontPageResult<WechatContactAuditVO> myList(WechatContactListReq req){
        User user = userService.getUserByContext();
        req.setCreateId(user.getUserId());
        IPage<InfluencerComWechat> influencerComWechatIPage = this.baseMapper.auditList(PageFactory.defaultPage(), req);
        IPage<WechatContactAuditVO> resultPage = new Page<>();
        FrontPageResult<WechatContactAuditVO> frontPageResult = new FrontPageResult<>();
        if (Objects.nonNull(influencerComWechatIPage) && 0 < influencerComWechatIPage.getTotal()) {
            BeanUtil.copyProperties(influencerComWechatIPage, resultPage,"records","optimizeCountSql","isSearchCount");
            List<InfluencerComWechat> records = influencerComWechatIPage.getRecords();
            List<WechatContactAuditVO> resultList = entityToVO(records);
            resultPage.setRecords(resultList);
            frontPageResult = FrontPageResult.convertPage(influencerComWechatIPage,e -> BeanUtil.toBean(e, WechatContactAuditVO.class));
            dataConvert(frontPageResult.getList());
        }
        return frontPageResult;
    }

    /**
     * 领取任务
     * @param req
     * @return
     */
    public TaskReceiveVO taskReceive(TaskReceiveReq req){

        RLock lock = redissonClient.getLock(RECEIVE_TASK_KEY);
        try {

            if (! lock.tryLock(5,30, TimeUnit.SECONDS)){
                throw new RuntimeException("系统繁忙,请稍后重试!");
            }
        } catch (InterruptedException e) {
            log.error("获取redis锁异常:",e);
            throw new RuntimeException(e);
        }
        try {
            List<InfluencerComWechat> pendingTaskList = this.baseMapper.taskListByReceiveStatus(req.getReceiveNum(), Arrays.asList(TaskReceiveStatus.PENDING.getValue()));
            if (CollectionUtil.isEmpty(pendingTaskList)){
                return TaskReceiveVO.builder().receiveNum(req.getReceiveNum()).successNum(0).message("暂无待领取任务!").build();
            }
            User user = userService.getUserByContext();
            Assert.notNull(user,"当前用户未登陆!");

            List<BizReceive> receiveList = new ArrayList<>();
            pendingTaskList.forEach(e ->{
                BizReceive bizReceive = new BizReceive();
                bizReceive.setBizNo(StrUtil.toString(e.getId()));
                bizReceive.setBizType(2);
                bizReceive.setReceiveId(StrUtil.toString(user.getUserId()));
                bizReceive.setReceiveName(user.getName());
                bizReceive.setBizStatus(0);
                bizReceive.setBizStatusName("");
                bizReceive.setRemark("");
                bizReceive.setRemark2("");
                bizReceive.setCreateTime(new Date());
                bizReceive.setUpdateTime(new Date());

                receiveList.add(bizReceive);

                e.setReceiveStatus(TaskReceiveStatus.RECEIVED.getValue());
                e.setAuditUserId(user.getUserId());
                e.setAuditUserName(user.getName());
            });

//            List<WfApprovalRecord> approvalRecords = new ArrayList<>();
//            pendingTaskList.forEach(e ->{
//                e.setReceiveStatus(TaskReceiveStatus.RECEIVED.getValue());
//                e.setAuditUserId(user.getUserId());
//                e.setAuditUserName(user.getName());
//
//                WfApprovalRecord approvalRecord = new WfApprovalRecord();
//                approvalRecord.setRequestId(System.currentTimeMillis());
//                approvalRecord.setBillNo(e.getId().toString());
//                approvalRecord.setBillType(ApprovalBillType.WechatContactApprove.getCode());
//                approvalRecord.setApproverId(user.getUserId());
//                approvalRecord.setApproverName(user.getName());
//                approvalRecord.setApprovalStatus(TaskApproveStatus.SUBMIT.getValue());
//                approvalRecord.setApprovalStatusName(TaskApproveStatus.SUBMIT.getName());
//                approvalRecord.setCreateTime(new Date());
//                approvalRecord.setUpdateTime(new Date());
//
//                approvalRecords.add(approvalRecord);
//            });
            receiveTaskSaveDB(pendingTaskList,receiveList);
            return TaskReceiveVO.builder().receiveNum(req.getReceiveNum()).successNum(pendingTaskList.size()).message("领取成功!").build();
        }finally {
            lock.unlock();
        }

    }


    /**
     * 任务回收
     * @param req
     * @return
     */
    @Transactional
    public void taskRecovery(TaskRecoveryReq req){
        LambdaQueryWrapper<InfluencerComWechat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InfluencerComWechat::getAuditUserId,req.getUserId());
        List<InfluencerComWechat> list = this.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)){
            List<String> comWechatIds = list.stream().map(e -> StrUtil.toString(e.getId())).collect(Collectors.toList());
            recoveryDataSaveDB(list);
            bizReceiveService.delByBizNoAndBizType(comWechatIds,2);
        }

    }

    /**
     * 审核
     * @param req
     * @return
     */
    public void audit(AuditOptReq req){
        User user = userService.getUserByContext();
        Assert.notNull(user,"当前用户未登陆!");
        Assert.notNull(req.getId(),"请求id为空!");
        auditDataSaveDB(req,user);
    }

    /**
     * 任务回收数据持久化
     * @param list 待回收任务列表
     */
    @Transactional
    public void recoveryDataSaveDB(List<InfluencerComWechat> list){
        if (CollectionUtil.isEmpty(list)){
            return ;
        }

        List<String> wechatContactIds = list.stream().map(e -> StrUtil.toString(e.getId())).collect(Collectors.toList());
        //领取表记录删除
        bizReceiveService.delByBizNoAndBizType(wechatContactIds,2);

        //任务领取状态改为待领取  审核人置空
        LambdaUpdateWrapper<InfluencerComWechat> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(InfluencerComWechat::getReceiveStatus,ReceiveType.PENDING.value);
        updateWrapper.set(InfluencerComWechat::getAuditUserId,null);
        updateWrapper.set(InfluencerComWechat::getAuditUserName,null);
        updateWrapper.in(InfluencerComWechat::getId,wechatContactIds);
        updateWrapper.eq(InfluencerComWechat::getAuditStatus,TaskApproveStatus.SUBMIT.getValue());
        this.update(updateWrapper);
    }

//    @Transactional
//    public void receiveTaskSaveDB(List<InfluencerComWechat> pendingTaskList,List<WfApprovalRecord> recordList){
//        this.updateBatchById(pendingTaskList);
//        wfApprovalRecordService.saveBatch(recordList);
//    }

    @Transactional
    public void receiveTaskSaveDB(List<InfluencerComWechat> pendingTaskList,List<BizReceive> receiveList){
        this.updateBatchById(pendingTaskList);
        bizReceiveService.saveBatch(receiveList);
    }

    @Transactional
    public void auditDataSaveDB(AuditOptReq req,User user){
        //历史数据标记为历史
        wfApprovalRecordService.loseEffectiveRecord(StrUtil.toString(req.getId()),ApprovalBillType.WechatContactApprove.getCode());

        WfApprovalRecord wfApprovalRecord = new WfApprovalRecord();
        wfApprovalRecord.setBillNo(StrUtil.toString(req.getId()));
        wfApprovalRecord.setBillType(ApprovalBillType.WechatContactApprove.getCode());
        wfApprovalRecord.setApproverId(user.getUserId());
        wfApprovalRecord.setApproverName(user.getName());
        wfApprovalRecord.setCreateTime(new Date());
        wfApprovalRecord.setUpdateTime(new Date());
        wfApprovalRecord.setHistory(0);
        wfApprovalRecord.setApprovalComment(req.getRejectedReason());
        wfApprovalRecord.setApprovalStatus(req.getOptType());
        wfApprovalRecordService.save(wfApprovalRecord);

        LambdaUpdateWrapper<InfluencerComWechat> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(InfluencerComWechat::getId,req.getId());
        updateWrapper.set(InfluencerComWechat::getAuditStatus,req.getOptType());
        updateWrapper.set(InfluencerComWechat::getAuditUserId,user.getUserId());
        updateWrapper.set(InfluencerComWechat::getAuditUserName,user.getName());
        updateWrapper.set(InfluencerComWechat::getRejectedReason,req.getRejectedReason());
        updateWrapper.set(InfluencerComWechat::getAuditTime,new Date());
        updateWrapper.set(InfluencerComWechat::getUpdateTime,new Date());

        this.update(updateWrapper);
    }


    private void  dataConvert(List<WechatContactAuditVO> data){
        if(CollectionUtil.isEmpty(data)){
            return ;
        }
        data.stream().forEach(e ->{
            e.setAuditStatusName(TaskApproveStatus.getName(e.getAuditStatus()));
            e.setInfluencerTypeName(InflucerType.getNameByValue(StrUtil.toString(e.getInfluencerType())));
        });
    }

    public static String delHttp(String url) {
        if (org.apache.commons.lang3.StringUtils.isBlank(url)) {
            return null;
        }
        String delUrl = url;
        if (url.startsWith("http")) {
            if (url.contains("/pc/")){
                String[] split = url.split("/pc/");
                delUrl = split[split.length - 1];
                return "pc/" + delUrl;
            }

            if (url.contains("/poi/")){
                String[] split = url.split("/poi/");
                delUrl = split[split.length - 1];
                return "poi/" + delUrl;
            }
        }
        return url;
    }


    private List<InfluencerComWechat> getWechatContactRecord(Long influencerId,Long platformId,Long projectId,Long createUserId){
        LambdaQueryWrapper<InfluencerComWechat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InfluencerComWechat::getInfluencerId,influencerId);
        queryWrapper.eq(InfluencerComWechat::getPlatformId,platformId);
        queryWrapper.eq(InfluencerComWechat::getProjectId,projectId);
        queryWrapper.eq(InfluencerComWechat::getCreateId,createUserId);
        return this.list(queryWrapper);
    }

    private List<InfluencerComWechat> getWechatContactRecord(Long submitApproveId){
        LambdaQueryWrapper<InfluencerComWechat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InfluencerComWechat::getSubmitApproveId,submitApproveId);
        return this.list(queryWrapper);
    }

    private List<WechatContactAuditVO> entityToVO(List<InfluencerComWechat> wechatContactList){
        if (CollectionUtil.isEmpty(wechatContactList)){
            return new ArrayList<>();
        }
        List<WechatContactAuditVO> voList = new ArrayList<>();
        wechatContactList.stream().forEach(e -> {
            WechatContactAuditVO wechatContactAuditVO = BeanUtil.copyProperties(e, WechatContactAuditVO.class);
            wechatContactAuditVO.setBusUserId(e.getCustomerId());
            wechatContactAuditVO.setBusUserName(e.getCustomerName());
            wechatContactAuditVO.setBusDept(e.getCustomerDeptId());
            wechatContactAuditVO.setPlatformNickName(e.getNickName());
            wechatContactAuditVO.setContactTime(e.getCreateTime());
            if (Objects.nonNull(e.getCustomerDeptId())){
                Dept dept = userIndexService.queryDeptById(e.getCustomerDeptId());
                if (Objects.nonNull(dept)){
                    wechatContactAuditVO.setBusDeptName(dept.getSimpleName()+"/"+e.getCustomerDeptName());
                }
            }else {
                wechatContactAuditVO.setBusDeptName(e.getCustomerDeptName());
            }
            voList.add(wechatContactAuditVO);
        });
        return voList;
    }
}
