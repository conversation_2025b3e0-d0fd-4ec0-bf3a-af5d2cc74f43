@layout("/common/_container.html",{js:["/pages/modular/settleProjects/settleProjects/js/fileupd.js","/pages/modular/cushionInfluencerOrder/cushionInfluencerOrder/js/cushionInfluencerOrder.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">垫付管理管理</span>
</div>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm12 layui-col-md12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-body">

                    <div class="layui-row layui-col-space15">
                        <!-- 左侧状态区域 -->
                        <div class="layui-col-md8">
                            @ include("/modular/cushionOrder/cushionOrder/cushionpjs.html"){}
                        </div>

                        <!-- 右侧附件区域 -->
                        <div class="layui-col-md4">
                            <div class="layui-panel">
                                <div class="layui-panel-header" style="border-bottom: 1px solid #eee; padding: 0 15px 15px;">
                                    <span>财务上传附件</span>
                                </div>

                                <input type="hidden" id="cwshhefujian" value="${cushionBatch.caiwuJisuanFile},${cushionBatch.caiwuFujianFiles}">
                                <div class="layui-panel-body">
                                    <ul class="layui-list" id="ulddf">
<!--                                        <li><i class="layui-icon layui-icon-file"></i> 7月热浪淘宝直播收益.xls</li>-->
                                    </ul>
                                    <div class="layui-text" style="background: #f2f2f2; padding: 10px; margin-top: 15px;">
                                        备注：${cushionBatch.caiwuRemark}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-header">项目营收数据</div>
                    <div class="layui-card-body">
                        <table class="layui-table">
                            <colgroup>
                                <col width="150">
                                <col width="200">
                                <col width="150">
                                <col>
                            </colgroup>
                            <tbody>
                            <tr>
                                <td>总营收金额：</td>
                                <td>${cushionOrder.liushuiAmount}</td>
                                <td>总应发放收益：</td>
                                <td>${cushionOrder.yingfaAmount}</td>
                                <td>总已发放收益：</td>
                                <td>${cushionOrder.yifaAmount}</td>
                                <td>参与结算人数：</td>
                                <td>${cushionOrder.cushionPnum}</td>
                            </tr>

                            <tr>
                                <td>达人已提现金额：</td>
                                <td>${cushionOrder.withdrawnAmount}</td>
                                <td>达人未提现金额：</td>
                                <td>${cushionOrder.unwithdrawnAmount}</td>
                                <td>达人已到账金额：</td>
                                <td>${cushionOrder.arriveAmount}</td>
                                <td>达人未到账金额：</td>
                                <td>${cushionOrder.noarriveAmount}</td>
                            </tr>

                            <tr>
                                <td>非标结算人数及比例：</td>
                                <td> ${ (isEmpty(cushionOrder.feibiaoPnum) && isEmpty(cushionOrder.feibiaoRate))
                                    ? "-"
                                    : cushionOrder.feibiaoPnum! + " ("+cushionOrder.feibiaoRate!+"%)" }
                                </td>
                                <td>非标结算金额及比例：</td>
                                <td> ${ (isEmpty(cushionOrder.feibiaoAmount) && isEmpty(cushionOrder.feibiaoAmountRate))
                                    ? "-"
                                    : cushionOrder.feibiaoAmount! + " ("+cushionOrder.feibiaoAmountRate!+"%)" }
                                </td>
                                <td>结算异常比例：</td>
                                <td> ${ (isEmpty(cushionOrder.cushionYichangNum) && isEmpty(cushionOrder.cushionYichangRate))
                                    ? "-"
                                    : cushionOrder.cushionYichangRate! + "%" }
                                </td>
                                <td></td>
                                <td></td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="layui-form toolbar">
                    <div class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">项目简称</label>
                                <div class="layui-input-inline">
                                    <input id="projectName" name="projectName" lay-filter="projectName" type="text" class="layui-input" autocomplete="off"/>
                                </div>
                            </div>

                            <div class="layui-inline" style="width: 400px">
                                <label class="layui-form-label">项目负责人</label>
                                <div class="layui-input-inline">
                                    <input id="businessContact" name="businessContact" placeholder="请输入businessContact" type="text" class="layui-input"/>
                                </div>
                            </div>

                            <div class="layui-inline" style="width: 400px">
                                <label class="layui-form-label">项目类型</label>
                                <div class="layui-input-inline">
                                    <input type="hidden" id="projectTypeId" name="projectTypeId" lay-filter="projectTypeId">
                                    <select id="projectTypeName" name="projectTypeName" lay-filter="projectTypeName" lay-search="">
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-inline" style="width: 400px">
                                <label class="layui-form-label" style="white-space: nowrap">总营收金额</label>
                                <div class="layui-input-inline">
                                    <select id="cushionStatus" name="cushionStatus" lay-filter="cushionStatus" lay-search="">
                                        <option VALUE=""></option>
                                        <option VALUE="1">10万以下</option>
                                        <option VALUE="2">10-20万</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button id="btnSearch" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <table class="layui-table" id="cushionInfluencerOrderTable" lay-filter="cushionInfluencerOrderTable"></table>
        </div>
    </div>
</div>
</div>
</div>

<script type="text/html" id="tableBar">
    
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</a>
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">发送短信</a>
</script>
@}

