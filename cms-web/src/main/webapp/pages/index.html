<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="${ctxPath}/assets/expand/images/favicon.ico" rel="icon">
    <title>${systemName}</title>
    <link rel="stylesheet" href="${ctxPath}/assets/common/layui/css/layui.css" />
    <link rel="stylesheet" href="${ctxPath}/assets/common/module/admin.css" />
    <link rel="stylesheet" href="${ctxPath}/assets/common/css/notice.css" />

</head>

<body class="layui-layout-body">

    <div class="layui-layout layui-layout-admin">

        <!-- 头部 -->
        @include("/common/_header.html"){}

        <!-- 侧边栏 -->
        @include("/common/_sidebar.html"){}

        <!-- 主体部分 -->
        @include("/common/_body.html"){}

        <!-- 底部11 -->


    </div>

    <!-- 加载动画，移除位置在common.js中 -->
    @include("/common/loading.html"){}

    @/* 加入contextPath属性和session超时的配置 */
    <script type="text/javascript">
        var Feng = {
            ctxPath: "",
            addCtx: function (ctx) {
                if (this.ctxPath === "") {
                    this.ctxPath = ctx;
                }
            }
        };
        Feng.addCtx("${ctxPath}");
    </script>
    <script type="text/javascript" src="${ctxPath}/assets/common/layui/layui.js"></script>
    <script type="text/javascript" src="${ctxPath}/assets/common/js/common.js"></script>

    <script>
        layui.use(['layer', 'element', 'admin', 'index'], function () {
            var $ = layui.jquery;
            var layer = layui.layer;
            var admin = layui.admin;
            var index = layui.index;

            admin.changeTheme('theme-white');
            // 默认加载主页
            index.loadHome({
                menuPath: '${ctxPath}/system/console',
                menuName: '<i class="layui-icon layui-icon-home"></i>'
            });

            // 修改密码点击事件
            $('#setPsw').click(function () {
                admin.open({
                    id: 'pswForm',
                    type: 2,
                    title: '修改密码',
                    shade: 0,
                    content: '${ctxPath}/system/user_chpwd'
                });
            });

            // 修改手机号点击事件
            $('#setPhone').click(function () {
                admin.open({
                    id: 'setPhoneForm',
                    type: 2,
                    title: '修改手机号',
                    shade: 0,
                    content: '${ctxPath}/system/user_set_phone'
                });
            });

            // 退出登录点击事件
            $('#btnLogout').click(function () {
                layer.confirm('确定退出登录？', {
                    skin: 'layui-layer-admin'
                }, function () {
                    window.location.href = "${ctxPath}/logout";
                });
            });
            var viewModel = {
                msgIds: [],
                userInfo: {}
            };
            viewModel.getUserInfo = function () {
                var xhr = new XMLHttpRequest();

                xhr.open('GET', Feng.ctxPath + '/system/currentUserInfo', true);

                xhr.onreadystatechange = function () {
                    // 请求完成且成功
                    if (xhr.readyState === 4) {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            try {
                                var data = JSON.parse(xhr.responseText);
                                data = data.data;
                                viewModel.userInfo = data;
                                var pre = data.avatar.indexOf("http") > -1 ? '' : 'https://mingyuehao.oss-cn-beijing.aliyuncs.com/';
                                if (data) {
                                    if (!!!data.avatar) {
                                        $('#avatar').attr('src', Feng.ctxPath + 'assets/common/images/user-default.png');
                                    } else {
                                        $('#avatar').attr('src', pre + data.avatar);
                                    }

                                    $('#userName').text(data.name);
                                } else {
                                    $('#avatar').attr('src', Feng.ctxPath + 'assets/common/images/user-default.png');
                                    $('#userName').text('游客');
                                }
                                console.log('用户详情:', data);
                            } catch (e) {
                                console.error('JSON 解析失败');
                            }
                        } else {
                            console.error('请求失败，状态码:', xhr.status);
                        }
                    }
                };

                xhr.send();
            }

            viewModel.getUserInfo();
            document.getElementById('messageListPopId').style.display = 'none';
            var reloadMsg = function (show) {
                var xhr = new XMLHttpRequest();

                xhr.open('GET', Feng.ctxPath + '/system/myNotice', true);

                xhr.onreadystatechange = function () {
                    // 请求完成且成功
                    if (xhr.readyState === 4) {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            try {
                                var data = JSON.parse(xhr.responseText);
                                data = data.data;
                                console.log("🚀 ~ reloadMsg ~ data:", data)
                                var list = data.list;
                                var total = data.total;
                                if (list) {
                                    var noticeList = document.getElementById('messageListId');
                                    noticeList.innerHTML = '';
                                    // 假设返回的数据是JSON格式的数组
                                    let output = '';
                                    viewModel.msgIds = [];
                                    if (list.length == 0) {
                                        $("#messageIcon").removeClass('shake-icon');
                                        output = '<h2 style="text-align:center">暂无数据</h2>';
                                    } else {
                                        document.getElementById('messageId').title = '您有' + total + '条未读消息，请及时处理';
                                        $("#messageIcon").addClass('shake-icon');
                                        list.forEach(function (item) {
                                            viewModel.msgIds.push(item.noticeId);
                                            output += ' <a class="message-list-item" href="javascript:;">';
                                            output += '  <img class="message-item-icon" src="${ctxPath}/assets/common/images/message.png">';
                                            output += ' <span class="message-item-right">';
                                            output += '<span class="message-item-title">' + item.title + '</span>';
                                            output += '<span class="message-item-text">' + item.createTime + '</span>';
                                            output += '  </span>';
                                            output += ' </a>';

                                        });
                                    }

                                    output += '';
                                    document.getElementById('messageListId').innerHTML = output;
                                    if (show == 2) {
                                        document.getElementById('messageListPopId').style.display = 'block';
                                        if (list.length == 0) {
                                            setTimeout(() => {
                                                document.getElementById('messageListPopId').style.display = 'none';

                                            }, 3000);
                                        }
                                    }
                                }
                            } catch (e) {
                                console.error('JSON 解析失败', e);
                            }
                        } else {
                            console.error('请求失败，状态码:', xhr.status);
                        }
                    }
                };

                xhr.send();
            };
            reloadMsg(1)
            setInterval(() => {
                reloadMsg(1)
            }, 10000);
            $("#allReadId").click(function () {
                $.ajax({
                    url: Feng.ctxPath + '/system/allRead',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(viewModel.msgIds),
                    success: function (data) {
                        reloadMsg(2);
                    },
                    error: function (error) {
                        layer.msg("操作失败", error);
                    }
                });
            })
            $("#messageId").click(function () {
                reloadMsg(2);
            })
            var modeIndex = {
                setMyInfo: -1,
                resetPassDiv: -1,
                submitSendValidCode: -1,
                changeMyPhone: -1
            }
            $("#setMyInfo").click(function (event) {
                $("#name").text(viewModel.userInfo.name)
                $("#avatar-preview").attr("src", viewModel.userInfo.avatar)
                $("#phoneNum").text(viewModel.userInfo.phone)
                $("#phone").text(viewModel.userInfo.phone)


                modeIndex.setMyInfo = layer.open({
                    type: 2,
                    title: "设置",
                    type: 1,
                    shade: false, // 不显示遮罩
                    area: ['900px', '800px'],
                    content: $('#USER-INFO-WRAPPER'), // 捕获的元素
                    end: function () {
                        // layer.msg('关闭后的回调', {icon:6});
                    }
                });
            });
            $("#changeMyPhone").click(function (event) {
                console.log("🚀 ~ changeMyPhone:", event)
                modeIndex.changeMyPhone = layer.open({
                    type: 1,
                    title: '修改手机号',
                    area: ['650px', '400px'],
                    shade: false, //不显示遮罩
                    shadeClose: false, //开启遮罩关闭
                    content: $('#changeMyPhoneDiv'), // 捕获的元素
                    end: function () {
                        // layer.msg('关闭后的回调', {icon:6});
                    }
                })

            });
            $("#sendValidCodeId").click(function (event) {
                //开启按钮倒计时
                var time = 60;
                var timer = setInterval(function () {
                    time--;
                    if (time <= 0) {
                        clearInterval(timer);
                        $("#sendValidCodeId").removeAttr("disabled");
                        $("#sendValidCodeId").html("发送验证码");
                    } else {
                        $("#sendValidCodeId").attr("disabled", "disabled");
                        $("#sendValidCodeId").html(time + "秒后重试");
                    }
                }, 1000)
                //  发送验证码
                $.ajax({
                    type: "POST",
                    url: Feng.ctxPath + '/user/userIndex/sendValidCode?phone=' + viewModel.userInfo.phone,
                    success: function (data) {
                        layer.msg("发送成功");

                    }
                })

            })
            $("#submitSendValidCode").click(function (event) {
                console.log("🚀 ~ submitSendValidCode:", event)
                $("#endPhoneId").val(viewModel.userInfo.phone)
                modeIndex.submitSendValidCode = layer.open({
                    type: 1,
                    title: '修改手机号',
                    area: ['650px', '400px'],
                    shade: false, //不显示遮罩
                    shadeClose: false, //开启遮罩关闭
                    content: $('#changeMyPhoneEndDiv'), // 捕获的元素
                    end: function () {
                        // layer.msg('关闭后的回调', {icon:6});
                        layer.close(modeIndex.changeMyPhone)


                    }

                })
            });
            $("#submitEndChangePhone").click(function (event) {
                $.ajax({
                    type: "POST",
                    url: Feng.ctxPath + '/user/userIndex/updateUserPhone?phone=' + viewModel.userInfo.phone + '&code=' + document.getElementById('endValidCodeId').value,
                    success: function (data) {
                        layer.msg("手机号修改成功")
                        layer.close(modeIndex.submitSendValidCode)
                    }
                })
            });


            $("#resetPass").click(function (event) {
                console.log("🚀 ~ resetPass:", event)
                modeIndex.resetPassDiv = layer.open({
                    type: 1,
                    title: '修改密码',
                    area: ['650px', '400px'],
                    shade: false, //不显示遮罩
                    shadeClose: false, //开启遮罩关闭
                    content: $('#resetPassDiv'), // 捕获的元素
                    end: function () {
                        // layer.msg('关闭后的回调', {icon:6});

                    }
                });

            });
            $("#submitResetPass").click(function (event) {
                console.log("🚀 ~ submitResetPass:", event)
                var pass = document.getElementById('passwordId').value
                var rwpass = document.getElementById('rwpasswordId').value
                if (pass != rwpass) {
                    layer.msg('两次密码不一致')
                    return false;
                }
                $.ajax({
                    type: "POST",
                    url: Feng.ctxPath + '/user/userIndex/updateUserPassword?password=' + document.getElementById('passwordId').value,
                    success: function (data) {
                        console.log("🚀 ~ data:", data);
                        layer.msg("密码修改成功");
                        layer.close(modeIndex.resetPassDiv)

                    }
                })

            });
            $("#avatar-click").click(function (event) {
                console.log("🚀 ~ avatar-click:", event)
                $("#fileUpload").click();

            });

            $("#fileUpload").change(function (event) {
                console.log("🚀 ~ fileUpload:", event)
                var file = event.target.files[0]
                var form = new FormData();
                form.append("file", file)
                $.ajax({
                    url: Feng.ctxPath + '/user/userIndex/updateUserAvatar',
                    type: "POST",
                    data: form,
                    processData: false,
                    contentType: false,
                    success: function (res) {
                        console.log("🚀 ~ success:", res)
                        $("#avatar-preview").attr("src", res.data);
                        layer.msg("头像修改成功")
                    }
                })


            });
            $("#cancelChangeMyPhoneDiv").click(function (event) {
                layer.close(modeIndex.changeMyPhone);

            });
            $("#cancelChangeMyPhoneEndDiv").click(function (event) {
                layer.close(modeIndex.submitSendValidCode);

            });
            $("#cancelResetPassDiv").click(function (event) {
                layer.close(modeIndex.resetPassDiv);

            });
        });
        document.addEventListener("DOMContentLoaded", function () {
            window.addEventListener("click", function (event) {
                console.log("🚀 ~ event:", event)
                var popup = document.getElementById('messageListPopId');

                if (!popup) {
                    console.error("未找到 ID 为 messageListPopId 的元素");
                    return;
                }

                // 如果点击的是弹出框或其子元素，则不隐藏
                if (!popup.contains(event.target)) {
                    popup.style.display = "none";
                }
            });
        });

    </script>
</body>

</html>