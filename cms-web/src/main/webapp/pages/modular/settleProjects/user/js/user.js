layui.use(['layer', 'form', 'table', 'ztree', 'laydate', 'admin', 'ax'], function () {
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var $ZTree = layui.ztree;
    var $ax = layui.ax;
    var laydate = layui.laydate;
    var admin = layui.admin;

    /**
     * 系统管理--用户管理
     */
    var MgrDpUser = {
        tableId: "userTable2",    //表格id
        condition: {
            name: "",
            deptId: "",
            timeLimit: ""
        }
    };

    /**
     * 初始化表格的列
     */
    MgrDpUser.initColumn = function () {
        return [[
            {type: 'radio'},
            {field: 'userId', hide: true, sort: true, title: '用户id'},
            {field: 'account', sort: true, title: '账号'},
            {field: 'name', sort: true, title: '姓名'},
            {field: 'sexName', hide: true, sort: true, title: '性别'},
            {field: 'roleName', sort: true, title: '角色'},
            {field: 'deptName', sort: true, title: '部门'},
            {field: 'email', hide: true, sort: true, title: '邮箱'},
            {field: 'phone', hide: true, sort: true, title: '电话'},
            {field: 'createTime', hide: true, sort: true, title: '创建时间'},
            {field: 'status', sort: true, templet: '#statusTpl', title: '状态'},
            {align: 'center', toolbar: '#tableBar', title: '操作', minWidth: 150}
        ]];
    };

    /**
     * 选择部门时
     */
    MgrDpUser.onClickDept = function (e, treeId, treeNode) {
        MgrDpUser.condition.deptId = treeNode.id;
        MgrDpUser.search();
    };

    /**
     * 点击查询按钮
     */
    MgrDpUser.search = function () {
        var queryData = {};
        queryData['deptId'] = MgrDpUser.condition.deptId;
        queryData['name'] = $("#name").val();
        queryData['timeLimit'] = $("#timeLimit").val();
        table.reload(MgrDpUser.tableId, {where: queryData});
    };

    MgrDpUser.addAsgns = function () {
        // 获取选中行的数据
        var checkRows = table.checkStatus(MgrDpUser.tableId); // tableId 是表格容器的 id
        debugger
        if (checkRows.data.length === 0) {
            Feng.error("请选择要新建的客服");
        } else {
            var idString = checkRows.data.map(item => item.userId)
                .filter((id, index, self) => self.indexOf(id) === index)
                .join(',');

            var nameString = checkRows.data.map(item => item.account)
                .filter((id, index, self) => self.indexOf(id) === index)
                .join(',');


            const accountString = checkRows.data.map(item => item.account).join(',');
            // debugger

            for(var i=0;i<window.parent.length;i++){
                var wd = window.parent[i];

                if(wd.assignNs){
                    wd.assignNs(idString,accountString);
                    wd.idString = idString;           // 修改父页面变量
                    wd.nameString = accountString;           // 修改父页面变量

                    top.layer.close(top.layer.index);
                    break;
                }
            }

        }
    };


    // 渲染表格
    var tableResult = table.render({
        elem: '#' + MgrDpUser.tableId,
        url: Feng.ctxPath + '/mgr/list',
        id: MgrDpUser.tableId, // 定义表格 id
        page: true,
        height: "full-98",
        cellMinWidth: 100,
        cols: MgrDpUser.initColumn()
    });

    //渲染时间选择框
    laydate.render({
        elem: '#timeLimit',
        range: true,
        max: Feng.currentDate()
    });

    //初始化左侧部门树
    var ztree = new $ZTree("deptTree", "/dept/tree");
    ztree.bindOnClick(MgrDpUser.onClickDept);
    ztree.init();

    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        console.log("点击搜索");
        MgrDpUser.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        console.log("点击添加")
        MgrDpUser.addAsgns();
    });

    // 工具条点击事件
    table.on('tool(' + MgrDpUser.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'addCus') {
            MgrDpUser.addAsgns(data);
        }
    });

});
