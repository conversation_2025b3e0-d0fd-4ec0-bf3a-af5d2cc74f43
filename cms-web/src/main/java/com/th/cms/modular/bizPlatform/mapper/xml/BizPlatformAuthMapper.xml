<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.th.cms.modular.bizPlatform.mapper.BizPlatformAuthMapper">


    <select id="basePlatformList"
            resultType="com.th.cms.modular.bizPlatform.entity.BizPlatformAuth">
        select
        id, plat_name as platName, icon_pic as iconPic, jiancheng, create_time as createTime, update_time as updateTime, status
        from biz_platform_auth
        where 1=1
        <if test="record.search != null and record.search != ''">
            and plat_name like concat('%',#{record.search},'%')
        </if>
        <if test="record.status!=null">
            and status = #{record.status}
        </if>
        order by plat_name
    </select>
</mapper>
