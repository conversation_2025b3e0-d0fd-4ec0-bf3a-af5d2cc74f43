package com.th.cms.modular.wf.wfInflucerSubmitApprove.service;

import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.config.web.UserContextHolder;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.DateUtils;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.bizClue.entity.BizClue;
import com.th.cms.modular.bizClue.service.BizClueService;
import com.th.cms.modular.enums.ApprovalBillType;
import com.th.cms.modular.enums.ApprovalStatus;
import com.th.cms.modular.enums.SettleAccountStatus;
import com.th.cms.modular.influcer.bizInflucer.model.BizInflucer;
import com.th.cms.modular.influcer.bizInflucer.model.enums.PlatformType;
import com.th.cms.modular.influcer.bizInflucer.service.BizInflucerService;
import com.th.cms.modular.influcer.bizInflucerProject.model.BizInflucerProject;
import com.th.cms.modular.influcer.bizInflucerProject.service.BizInflucerProjectService;
import com.th.cms.modular.settle.settleAccount.model.SettleAccount;
import com.th.cms.modular.settle.settleAccount.service.SettleAccountService;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjectsInflucer;
import com.th.cms.modular.settle.settleProjects.mapper.SettleProjectsInflucerMapper;
import com.th.cms.modular.settle.settleProjects.service.SettleProjectsService;
import com.th.cms.modular.sysmesage.third.WfApproveMessageHelper;
import com.th.cms.modular.system.entity.Dept;
import com.th.cms.modular.system.entity.User;
import com.th.cms.modular.system.service.DeptService;
import com.th.cms.modular.system.service.UserService;
import com.th.cms.modular.wf.bizCustomizeForm.model.BizCustomizeForm;
import com.th.cms.modular.wf.bizCustomizeForm.service.BizCustomizeFormService;
import com.th.cms.modular.wf.bizCustomizeRecord.model.BizCustomizeRecord;
import com.th.cms.modular.wf.bizCustomizeRecord.service.BizCustomizeRecordService;
import com.th.cms.modular.wf.wfApprovalRecord.model.WfApprovalRecord;
import com.th.cms.modular.wf.wfApprovalRecord.service.WfApprovalRecordService;
import com.th.cms.modular.wf.wfApprovalRequest.model.WfApprovalRequest;
import com.th.cms.modular.wf.wfApprovalRequest.service.WfApprovalRequestService;
import com.th.cms.modular.wf.wfApprovalRequestView.model.WfApprovalRequestView;
import com.th.cms.modular.wf.wfApprovalRequestView.service.WfApprovalRequestViewService;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.dao.WfInflucerSubmitApproveMapper;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.*;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.enums.InflucerType;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.enums.OperatePermType;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.enums.OperateType;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.reqparam.WfInflucerSubmitApproveListParam;
import com.th.cms.modular.wf.wfStep.model.StepNodeType;
import com.th.cms.modular.wf.wfStep.model.WfStep;
import com.th.cms.modular.wf.wfStep.model.WfStepConfig;
import com.th.cms.modular.wf.wfStep.service.WfStepConfigService;
import com.th.cms.modular.wf.wfStep.service.WfStepService;
import com.th.cms.modular.wf.wfType.model.WfType;
import com.th.cms.modular.wf.wfType.service.WfTypeService;
import com.th.cms.util.UserDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 达人提审记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class WfInflucerSubmitApproveService extends ServiceImpl<WfInflucerSubmitApproveMapper, WfInflucerSubmitApprove> implements IService<WfInflucerSubmitApprove> {

    private static final Logger log = LoggerFactory.getLogger(WfInflucerSubmitApproveService.class);
    @Resource
    private WfApprovalRequestService wfApprovalRequestService;
    @Resource
    private SettleProjectsService settleProjectsService;
    @Resource
    private WfTypeService wfTypeService;
    @Resource
    private WfStepService wfStepService;
    @Resource
    private BizCustomizeFormService bizCustomizeFormService;
    @Resource
    private BizCustomizeRecordService bizCustomizeRecordService;
    @Autowired
    private WfApprovalRecordService wfApprovalRecordService;
    @Autowired
    private WfStepConfigService wfStepConfigService;
    @Autowired
    private WfApprovalRequestViewService wfApprovalRequestViewService;
    @Autowired
    private DeptService deptService;
    @Autowired
    private UserService userService;
    @Autowired
    private BizClueService bizClueService;
    @Autowired
    private BizInflucerProjectService bizInflucerProjectService;
    @Autowired
    private BizInflucerService bizInflucerService;
    @Autowired
    private SettleProjectsInflucerMapper settleProjectsInflucerMapper;
    @Autowired
    private WfApproveMessageHelper messageSendHelper;
    @Autowired
    private SettleAccountService settleAccountService;


    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(WfInflucerSubmitApproveListParam param) {

        Long userId = UserContextHolder.getUserId();

        if (userId == null) {
            return new LayuiPageInfo();
        }

        String updateTime = param.getUpdateTime();
        Date startTime = null;
        Date endTime = null;
        if (!StringUtils.isEmpty(updateTime)) {
            String[] split = updateTime.split(",");
            startTime = DateUtils.parse(split[0], DateUtils.PATTERN_YMD);
            endTime = DateUtils.parse(split[1], DateUtils.PATTERN_YMD);
        }

        QueryWrapper<WfInflucerSubmitApprove> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.lambda()

                // 添加多条件 like 查询 》手机号、合作平台ID、昵称、外战平台id、外战昵称
                .and(!StringUtils.isEmpty(param.getKeywords()), wrapper -> wrapper
                        .like(WfInflucerSubmitApprove::getPhone, "%" + param.getKeywords() + "%")
                        .or()
                        .like(WfInflucerSubmitApprove::getOutId, "%" + param.getKeywords() + "%")
                        .or()
                        .like(WfInflucerSubmitApprove::getOutName, "%" + param.getKeywords() + "%")
                        .or()
                        .like(WfInflucerSubmitApprove::getCoopName, "%" + param.getKeywords() + "%")
                        .or()
                        .like(WfInflucerSubmitApprove::getPlatformName, "%" + param.getKeywords() + "%")
                )
                .eq(!StringUtils.isEmpty(param.getSelf()) && "1".equals(param.getSelf()),//是否我的提审
                        WfInflucerSubmitApprove::getCreateId, userId)
                .eq(!StringUtils.isEmpty(param.getInflucerType()),//达人类型
                        WfInflucerSubmitApprove::getInflucerType, param.getInflucerType())
                .eq(!StringUtils.isEmpty(param.getPlatformId()),//外战平台
                        WfInflucerSubmitApprove::getPlatformId, param.getPlatformId())
                .eq(!StringUtils.isEmpty(param.getStatus()),//流程状态
                        WfInflucerSubmitApprove::getStatusName, param.getStatus())
                .like(!StringUtils.isEmpty(param.getOutPlatFormName()),//外战平台名称
                        WfInflucerSubmitApprove::getOutPlatformName, "%" + param.getOutPlatFormName() + "%")
                .gt(startTime != null, WfInflucerSubmitApprove::getUpdateTime, startTime)
                .lt(endTime != null, WfInflucerSubmitApprove::getUpdateTime, endTime)
                .orderByDesc(WfInflucerSubmitApprove::getUpdateTime);

        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);
        List<WfInflucerSubmitApprove> data = layuiPageInfo.getData();

        //达人信息
        Map<Long, BizInflucer> influcerMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(data)) {
            List<Long> influIds = data.stream().map(WfInflucerSubmitApprove::getInfluId).collect(Collectors.toList());
            QueryWrapper<BizInflucer> influcerQueryWrapper = new QueryWrapper<>();
            influcerQueryWrapper.lambda().select(BizInflucer::getId, BizInflucer::getNikeName).in(BizInflucer::getId, influIds);
            List<BizInflucer> list = bizInflucerService.list(influcerQueryWrapper);
            if (!CollectionUtils.isEmpty(list)) {
                influcerMap = list.stream().collect(Collectors.toMap(BizInflucer::getId, a -> a));
            }
        }

        for (WfInflucerSubmitApprove datum : data) {
            //达人昵称
            if (!CollectionUtils.isEmpty(influcerMap) && influcerMap.containsKey(datum.getInfluId())) {
                datum.setInflucerName(influcerMap.get(datum.getInfluId()).getNikeName());
            }

            //达人类型
            String influcerType = datum.getInflucerType();
            InflucerType type = InflucerType.getType(influcerType);
            if (type != null) {
                datum.setInflucerTypeName(type.name);
            }
            //操作权限
            List<String> operatePermList = new ArrayList<>();
            operatePermList.add(OperatePermType.READ.name);
            operatePermList.add(OperatePermType.LINK.name);

            //TODO 根据阶段ID，审批状态，结合审批记录 动态返回字段
            if (datum.getRequestId() != null && datum.getStepId() != null && datum.getStatus() != null) {

                QueryWrapper<WfApprovalRecord> recordQueryWrapper = new QueryWrapper<>();
                recordQueryWrapper.lambda().eq(WfApprovalRecord::getRequestId, datum.getRequestId())
                        .eq(WfApprovalRecord::getStepId, datum.getStepId());
                List<WfApprovalRecord> approvalRecords = wfApprovalRecordService.list(recordQueryWrapper);

                //我的提审逻辑-返回绑定、运营绑定操作标记位
                if (!StringUtils.isEmpty(param.getSelf()) && "1".equals(param.getSelf())) {

                    //提审审核逻辑-返回审批标记位
                    if (!CollectionUtils.isEmpty(approvalRecords)) {
                        for (WfApprovalRecord approvalRecord : approvalRecords) {

                            //待邀约 待提交
                            if (approvalRecord.getStepName().equals(OperateType.INVITE.name) &&
                                    (approvalRecord.getApprovalStatus().equals(ApprovalStatus.PENDING_SUBMIT.getCode()) ||
                                            approvalRecord.getApprovalStatus().equals(ApprovalStatus.UNDER_REVIEW.getCode()) ||
                                            approvalRecord.getApprovalStatus().equals(ApprovalStatus.RE_UNDER_REVIEW.getCode()))) {
                                operatePermList.add(OperatePermType.DEL.name);
                            }

                            //邀约拒绝
                            if (approvalRecord.getStepName().equals(OperateType.INVITE.name) &&
                                    approvalRecord.getApprovalStatus().equals(ApprovalStatus.REJECTED.getCode())) {
                                operatePermList.add(OperatePermType.MOD.name);
                                operatePermList.add(OperatePermType.DEL.name);
                            }

                            //待确认状态
                            if (ApprovalStatus.PENDING_SUBMIT.getCode().equals(approvalRecord.getApprovalStatus()) &&
                                    datum.getCreateId().equals(userId)) {
                                setOperateType(operatePermList, datum, approvalRecord.getStepName());
                            }

                            //审批单拒绝显示逻辑
                            if (ApprovalStatus.REJECTED.getCode().equals(datum.getStatus()) && datum.getCreateId().equals(userId)) {

                                WfStep rejectedStep = wfStepService.getById(datum.getStepId());

                                setOperateType(operatePermList, datum, rejectedStep.getStepName());
                            }
                        }
                    }
                } else {

                    //提审---审核逻辑-返回审批标记位
                    if (!CollectionUtils.isEmpty(approvalRecords)) {
                        for (WfApprovalRecord approvalRecord : approvalRecords) {
                            //待审批状态
                            if (ApprovalStatus.UNDER_REVIEW.getCode().equals(approvalRecord.getApprovalStatus()) &&
                                    approvalRecord.getApproverId().equals(userId)) {
                                setOperateType(operatePermList, datum, approvalRecord.getStepName());
                            }
                        }
                    }
                }
            }

            if (null == datum.getOperateType()) {
                datum.setOperateType(OperateType.VIEW.value);
            }

            //根据节点流程的权限限制
            datum.setOperatePerm(operatePermList);
        }


        return layuiPageInfo;
    }

    private static void setOperateType(List<String> operatePermList, WfInflucerSubmitApprove datum, String stepName) {
        if (stepName.equals(OperateType.INVITE.name)) {
            datum.setOperateType(OperateType.INVITE.value);
        } else if (stepName.equals(OperateType.BAND.name)) {
            datum.setOperateType(OperateType.BAND.value);
        } else if (stepName.equals(OperateType.RUN_BAND.name)) {
            datum.setOperateType(OperateType.RUN_BAND.value);
        }
    }

    /**
     * 创建提审记录
     *
     * @param dto
     * @return
     */
    public String submitApprove(UploadSubmitApproveDTO dto) throws IllegalAccessException {

        Long userId = UserContextHolder.getUserId();
        if (userId == null) {
            return "请登录明月号管理系统！";
        }

        //听海系统平台
        Long projectId = dto.getProjectId();
        SettleProjects settleProjects = settleProjectsService.getById(projectId);

        //判断项目是否存在
        if (settleProjects == null) {
            return "请核对当前项目是否已立项！";
        }

        List<SubmitFormFieldDTO> formFields = dto.getFormFields();

        //构建公共字段逻辑，其余字段放置 BizCustomizeForm BizCustomizeRecord
        WfInflucerSubmitApprove influcerSubmitApprove = new WfInflucerSubmitApprove();
        if (null != dto.getId()) {
            //重新提交生效
            influcerSubmitApprove = getById(dto.getId());
        }
        buildSubmitApproveOrder(influcerSubmitApprove, formFields);

        String phone = influcerSubmitApprove.getPhone();
        if (StringUtils.isEmpty(phone)) {
            return "手机号请勿为空！";
        }

        //合作平台判断
        if (StringUtils.isEmpty(influcerSubmitApprove.getCoopId()) && StringUtils.isEmpty(influcerSubmitApprove.getCoopName())) {
            return "合作平台ID、昵称请勿同时为空！";
        }

        //外站平台name
        String outPlatformId = influcerSubmitApprove.getOutPlatformId();
        if (!StringUtils.isEmpty(outPlatformId)) {
            PlatformType type = PlatformType.getType(Long.parseLong(outPlatformId));
            if (type != null) {
                influcerSubmitApprove.setOutPlatformName(type.name);
            } else {
                influcerSubmitApprove.setOutPlatformName("其他");
            }
        }

        //填充提审单信息
        richSubmitApprove(influcerSubmitApprove, settleProjects);

        //获取审批流信息
        WfType wfType = wfTypeService.getById(settleProjects.getFlowId());

        if (wfType == null) {
            return "请核对当前项目是否绑定审批流！";
        }
        influcerSubmitApprove.setFlowName(wfType.getTypeName());

        //重新提交逻辑
        if (null != dto.getId()) {

            //设置原提审单id
            influcerSubmitApprove.setId(dto.getId());

            //发起审批流开始流程
            startSubmitApproveFlow(wfType, influcerSubmitApprove, false);

            //更新状态数据
            updateById(influcerSubmitApprove);

            messageSendHelper.submitApprove(influcerSubmitApprove, userId);//WORKFLOW_SUBMIT  WORKFLOW_APPROVE
        } else {
            //首次提交⬇️
            //保存提审单，关联数据使用到id
            save(influcerSubmitApprove);

            try {

                //发起审批流开始流程
                startSubmitApproveFlow(wfType, influcerSubmitApprove, true);
                //更新状态数据
                updateById(influcerSubmitApprove);

            } catch (Exception e) {

                //删除审批记录
                QueryWrapper<WfApprovalRecord> recordQueryWrapper = new QueryWrapper<>();
                recordQueryWrapper.lambda().eq(WfApprovalRecord::getRequestId, influcerSubmitApprove.getRequestId());
                wfApprovalRecordService.remove(recordQueryWrapper);

                //出现异常情况，删除提审记录
                removeById(influcerSubmitApprove.getId());
                return e.getMessage();
            }

            messageSendHelper.submitApprove(influcerSubmitApprove, userId);
        }

        //保存提审单所有信息
        List<BizCustomizeRecord> customizeRecords = buildFormValues(influcerSubmitApprove.getId(), formFields);
        if (!CollectionUtils.isEmpty(customizeRecords)) {
            bizCustomizeRecordService.saveBatch(customizeRecords);
        }


        //更新线索的入驻平台数据 biz_clue
        String outLink = influcerSubmitApprove.getOutLink();
        if (!StringUtils.isEmpty(outLink)) {
            QueryWrapper<BizClue> clueQueryWrapper = new QueryWrapper<>();
            clueQueryWrapper.lambda().eq(BizClue::getStatus, 1).like(BizClue::getUrl, outLink.trim() + "%");
            List<BizClue> list = bizClueService.list(clueQueryWrapper);
            if (!CollectionUtils.isEmpty(list)) {
                for (BizClue bizClue : list) {

                    //更新外站信息
                    if (!StringUtils.isEmpty(bizClue.getNickname())) {
                        influcerSubmitApprove.setOutName(bizClue.getNickname());
                    }
                    if (!StringUtils.isEmpty(bizClue.getAccount())) {
                        influcerSubmitApprove.setOutId(bizClue.getAccount());
                    }
                    if (!StringUtils.isEmpty(bizClue.getAvatar())) {
                        influcerSubmitApprove.setOutAvatar(bizClue.getAvatar());
                    }
                    updateById(influcerSubmitApprove);

                    //更新线索，已入驻平台
                    String yetPlatform = bizClue.getYetPlatform();
                    if (!StringUtils.isEmpty(yetPlatform)) {
                        String[] split = yetPlatform.split(",");

                        List<String> yetPlatformList = Arrays.asList(split);
                        if (!yetPlatformList.contains(influcerSubmitApprove.getPlatformName())) {
                            yetPlatformList.add(influcerSubmitApprove.getPlatformName());
                            bizClue.setYetPlatform(String.join(",", yetPlatformList));
                            bizClue.setYetPlatformCount(yetPlatformList.size());

                            bizClueService.updateById(bizClue);
                        }
                    }
                }
            }
        }


        return "";
    }

    private void richSubmitApprove(WfInflucerSubmitApprove influcerSubmitApprove, SettleProjects settleProjects) {

        //判断达人信息
        BizInflucer bizInflucer = bizInflucerService.getInflucerByPhone(influcerSubmitApprove.getPhone());

        influcerSubmitApprove.setInfluId(bizInflucer.getId());
        influcerSubmitApprove.setInflucerId(bizInflucer.getInflucerId());
        influcerSubmitApprove.setInflucerName(bizInflucer.getNikeName());

        //合作平台
        influcerSubmitApprove.setCoopId(influcerSubmitApprove.getCoopId());//合作平台id
        influcerSubmitApprove.setCoopName(influcerSubmitApprove.getCoopName());//合作平台昵称
        influcerSubmitApprove.setPlatformId(!StringUtils.isEmpty(settleProjects.getPlatformId()) ? Long.parseLong(settleProjects.getPlatformId()) : null);
        influcerSubmitApprove.setPlatformName(settleProjects.getPlatform());


        influcerSubmitApprove.setWechatStatus(null);//TODO 同步企微建联状态
        influcerSubmitApprove.setCreateTime(new Date());
        influcerSubmitApprove.setUpdateTime(new Date());
        influcerSubmitApprove.setProjectId(Long.parseLong(settleProjects.getId().toString()));
        influcerSubmitApprove.setProjectName(settleProjects.getProjectName());


        //商务信息
        influcerSubmitApprove.setCreateId(UserContextHolder.getUserId());
        User user = userService.getById(influcerSubmitApprove.getCreateId());
        Dept dept = deptService.getById(user.getDeptId());
        List<String> deptNameList = new ArrayList<>();
        deptService.getBusinessInfo(dept, deptNameList);
        String bussiSource = String.join("/", deptNameList);
        //商务信息
        influcerSubmitApprove.setBussiName(UserContextHolder.getUserName());
        influcerSubmitApprove.setBussiSource(bussiSource);

        Integer count = settleAccountService.lambdaQuery()
                .eq(SettleAccount::getUserId, bizInflucer.getId())
                .eq(SettleAccount::getUserTel, bizInflucer.getLoginTel())
                .count();

        if (count == 0) {

            //添加账户信息
            SettleAccount settleAccount = new SettleAccount();

            settleAccount.setUserId(bizInflucer.getId());
            settleAccount.setNickName(bizInflucer.getNikeName());
            settleAccount.setUserTel(bizInflucer.getLoginTel());

            settleAccount.setAccountStatus(SettleAccountStatus.nomal.getCode());
            settleAccount.setAccountStatusName(SettleAccountStatus.nomal.getName());
            settleAccount.setCreateId(UserContextHolder.getUserId().toString());

            settleAccount.setZongAmount(BigDecimal.valueOf(0.00));
            settleAccount.setTixianAmount(BigDecimal.valueOf(0.00));
            settleAccount.setAmount(BigDecimal.valueOf(0.00));
            settleAccount.setDianfuAmount(BigDecimal.valueOf(0.00));
            settleAccount.setAccountNo(settleAccountService.genAccNo(bizInflucer.getId()));
            settleAccount.setCreateTime(new Date());
            settleAccount.setUpdateTime(new Date());

            settleAccountService.save(settleAccount);
        }

    }

    private List<BizCustomizeRecord> buildFormValues(Long approveId, List<SubmitFormFieldDTO> formFields) {

        return formFields.stream()
                .map(ff -> new BizCustomizeRecord()
                        .setApproveId(approveId)
                        .setRecordType(ff.getType())
                        .setRecordName(ff.getName())
                        .setRecordValue(ff.getValue())
                        .setCreateTime(new Date()))
                .collect(Collectors.toList());
    }

    private WfInflucerSubmitApprove buildSubmitApproveOrder(WfInflucerSubmitApprove submitApprove,
                                                            List<SubmitFormFieldDTO> formFields)
            throws IllegalAccessException {

        //反射构建提审单公共数据

        if (!CollectionUtils.isEmpty(formFields)) {

            Field[] declaredFields = submitApprove.getClass().getDeclaredFields();

            for (Field declaredField : declaredFields) {

                ExcelField annotation = declaredField.getAnnotation(ExcelField.class);

                if (annotation != null && !StringUtils.isEmpty(annotation.title())) {

                    for (SubmitFormFieldDTO formField : formFields) {
                        if (formField.getCode().equals(annotation.title())) {

                            declaredField.setAccessible(true);

                            Class<?> fieldType = declaredField.getType();

                            // 根据字段类型进行不同处理
                            Object valueToSet = null;
                            if (fieldType == Long.class || fieldType == long.class) {
                                // 处理Long类型
                                valueToSet = Long.valueOf(formField.getValue());
                            } else if (fieldType == Integer.class || fieldType == int.class) {
                                // 处理Integer类型
                                valueToSet = Integer.valueOf(formField.getValue());
                            } else if (fieldType == String.class) {
                                // 处理String类型
                                valueToSet = formField.getValue();
                            } else {
                                // 可以添加其他类型的处理逻辑
                                throw new IllegalArgumentException("不支持的字段类型: " + fieldType.getName());
                            }

                            // 设置字段值
                            declaredField.set(submitApprove, valueToSet);
                        }
                    }
                }
            }
        }

        return submitApprove;
    }

    private void startSubmitApproveFlow(WfType wfType, WfInflucerSubmitApprove influcerSubmitApprove, Boolean newFlow) {

        WfApprovalRequest approvalRequest = null;
        if (newFlow) {
            //创建审批单
            approvalRequest = new WfApprovalRequest()
                    .setTitle(wfType.getTypeName())
                    .setApplicantId(String.valueOf(UserContextHolder.getUserId()))
                    .setApplicantName(UserContextHolder.getUserName())
                    .setBillType(wfType.getType())
                    .setContent(wfType.getDescription())
                    .setBillName(ApprovalBillType.SubmitApprove.getName())
                    .setBillNo("TH-" + ApprovalBillType.SubmitApprove.getName() + "-" + influcerSubmitApprove.getId())
                    .setRequestStatus(ApprovalStatus.UNDER_REVIEW.getCode())
                    .setRequestStatusName(ApprovalStatus.UNDER_REVIEW.getDescription())
                    .setCreateTime(new Date());
            wfApprovalRequestService.save(approvalRequest);

            //创建审批记录
            wfStepService.generateApprovalRecords(influcerSubmitApprove, approvalRequest, wfType.getId());
            influcerSubmitApprove.setRequestId(approvalRequest.getId());
        } else {

            //重新提交
            approvalRequest = wfApprovalRequestService.getById(influcerSubmitApprove.getRequestId());
            approvalRequest.setUpdateTime(new Date());
            approvalRequest.setRequestStatus(ApprovalStatus.UNDER_REVIEW.getCode());
            approvalRequest.setRequestStatusName(ApprovalStatus.UNDER_REVIEW.getDescription());
            wfApprovalRequestService.updateById(approvalRequest);

            //归档历史审批记录
            UpdateWrapper<WfApprovalRecord> recordUpdateWrapper = new UpdateWrapper<>();
            recordUpdateWrapper.lambda().set(WfApprovalRecord::getHistory, 1).eq(WfApprovalRecord::getRequestId, approvalRequest.getId());
            wfApprovalRecordService.update(recordUpdateWrapper);

            //删除可见记录
            UpdateWrapper<WfApprovalRequestView> viewUpdateWrapper = new UpdateWrapper<>();
            viewUpdateWrapper.lambda().eq(WfApprovalRequestView::getRequestId, approvalRequest.getId());
            wfApprovalRequestViewService.remove(viewUpdateWrapper);

            //创建审批记录
            WfStep wfStep = wfStepService.generateApprovalRecords(influcerSubmitApprove, approvalRequest, wfType.getId());

            //设置重新提交-提审单标记位
            influcerSubmitApprove.setStatus(ApprovalStatus.UNDER_REVIEW.getCode());
            influcerSubmitApprove.setStatusName(wfStepService.getStatusName(wfStep, null,
                    ApprovalStatus.RE_UNDER_REVIEW.getCode()));
        }


        influcerSubmitApprove.setRequestId(approvalRequest.getId());
    }

    public SubmitApproveVO approveDetail(Long id) {

        //用户信息
        Long userId = UserContextHolder.getUserId();
        if (userId == null) {
            return SubmitApproveVO.builder().message("请登录明月号管理系统！").build();
        }

        //提审单基本信息
        WfInflucerSubmitApprove influcerSubmitApprove = getById(id);
        if (influcerSubmitApprove == null || influcerSubmitApprove.getRequestId() == null) {
            return SubmitApproveVO.builder().message("无效的提审信息，请核对后重试！").build();
        }

        QueryWrapper<WfStepConfig> configQueryWrapper = new QueryWrapper<>();
        configQueryWrapper.lambda().eq(WfStepConfig::getStepId, influcerSubmitApprove.getStepId());
        List<WfStepConfig> list = wfStepConfigService.list(configQueryWrapper);
        WfStepConfig formConfig = null;
        if (!CollectionUtils.isEmpty(list)) {
            if (list.size() == 1) {
                formConfig = list.get(0);
            } else {
                Optional<WfStepConfig> first = list.stream().filter(f -> !f.getIsEdit().equals(1)).findFirst();
                if (first.isPresent()) {
                    formConfig = first.get();
                } else {
                    Optional<WfStepConfig> first1 = list.stream().filter(f -> f.getIsEdit().equals(1)).findFirst();
                    formConfig = first1.get();
                }
            }
        }

        QueryWrapper<BizCustomizeRecord> dataQueryWrapper = new QueryWrapper<>();
        dataQueryWrapper.lambda().eq(BizCustomizeRecord::getApproveId, id);
        List<BizCustomizeRecord> records = bizCustomizeRecordService.list(dataQueryWrapper);

        //获取字段基本配置
        QueryWrapper<BizCustomizeForm> formQueryWrapper = new QueryWrapper<>();
        formQueryWrapper.lambda().eq(BizCustomizeForm::getStepId, influcerSubmitApprove.getStepId());
        List<BizCustomizeForm> formList = bizCustomizeFormService.list(formQueryWrapper);
        Map<String, List<BizCustomizeForm>> formGroup = formList.stream().collect(Collectors.groupingBy(BizCustomizeForm::getName));

        //数据记录
        List<SubmitFormFieldDTO> formFields = records.stream()
                .map(r -> SubmitFormFieldDTO.builder()
                        .code(formGroup.containsKey(r.getRecordName()) ? formGroup.get(r.getRecordName()).get(0).getCode() : "")
                        .type(r.getRecordType())
                        .name(r.getRecordName())
                        .value(r.getRecordValue())
                        .build()
                )
                .collect(Collectors.toList());

        //审批记录
        WfType tempType = new WfType();
        tempType.setCreateId(influcerSubmitApprove.getCreateId());
        wfTypeService.buildApproveRecordDetail(influcerSubmitApprove.getRequestId().toString(), tempType);

        //TODO 设置返回值信息


        QueryWrapper<SettleProjects> projectsQueryWrapper = new QueryWrapper<>();
        projectsQueryWrapper.lambda()
                .select(SettleProjects::getProjectName)
                .eq(SettleProjects::getId, influcerSubmitApprove.getProjectId());
        ;
        SettleProjects one = settleProjectsService.getOne(projectsQueryWrapper);
        //状态构建逻辑
        //返回状态标记-
        return SubmitApproveVO.builder()
                .id(influcerSubmitApprove.getId())
                .formConfig(formConfig)
                .formFields(formFields)
                .projectId(influcerSubmitApprove.getProjectId())
                .projectName(one != null ? one.getProjectName() : "")
                .recordId(tempType.getRecordId())
                .operateFlag(tempType.getOperateFlag())
                .statusName(influcerSubmitApprove.getStatusName())
                .approveRecordList(tempType.getApproveRecordList())
                .build();
    }

    /**
     * 审批
     *
     * @param dto
     */
    public String approve(SubmitApproveRequestDTO dto) {

        String eventType = dto.getEventType();//同意、驳回

        if (StringUtils.isEmpty(eventType)) {
            return "请选择操作类型！";
        }

        ApprovalStatus approvalStatus = buildApprovalStatus(eventType);

        if (approvalStatus == null) {
            return "无效的审批类型，请核对后重试！";
        }

        String approvalComment = null;
        List<SubmitFormFieldDTO> formFields = dto.getFormFields();
        if (dto.getSkipCheckForm() == null || !dto.getSkipCheckForm()) {
            if (CollectionUtils.isEmpty(formFields)) {
                return "表单信息请勿为空！";
            }

            approvalComment = setApprovalComment(formFields);
            if (ApprovalStatus.REJECTED == approvalStatus) {
                if (StringUtils.isEmpty(approvalComment)) {
                    return "审批驳回，请输入驳回的原因！";
                }
            }
        }

        //审批前的数据
        WfInflucerSubmitApprove influcerSubmitApprove = getById(dto.getId());
        if (influcerSubmitApprove == null) {
            return "无效的审批数据，请核对后重试！";
        }

        //设置最新备注信息
        if (!StringUtils.isEmpty(approvalComment)) {
            influcerSubmitApprove.setRemark(approvalComment);
        }

        //保存最新记录
        if (!CollectionUtils.isEmpty(formFields)) {
            QueryWrapper<BizCustomizeRecord> customizeRecordQueryWrapper = new QueryWrapper<>();
            customizeRecordQueryWrapper.lambda().eq(BizCustomizeRecord::getApproveId, dto.getId());
            bizCustomizeRecordService.remove(customizeRecordQueryWrapper);

            List<BizCustomizeRecord> records = new ArrayList<>();
            for (SubmitFormFieldDTO formField : formFields) {
                BizCustomizeRecord record = new BizCustomizeRecord();
                record.setApproveId(dto.getId());
                record.setRecordType(formField.getType());
                record.setRecordName(formField.getName());
                record.setRecordValue(formField.getValue());
                record.setCreateTime(new Date());
                records.add(record);
            }
            bizCustomizeRecordService.saveBatch(records);
        }

        //获取审批关键信息
        //审批单id，当前节点，下一审批节点
        Long recordId = dto.getRecordId();
        WfApprovalRecord approvalRecord = wfApprovalRecordService.getById(recordId);
        //审批状态 approvalStatus
        //审批附件
        //审批备注 approvalComment

        if (approvalStatus == ApprovalStatus.APPROVED || approvalStatus == ApprovalStatus.REJECTED) {

            //审批 同意/驳回
            return approvedOrRejectedApproval(influcerSubmitApprove, approvalRecord, approvalStatus, approvalComment);
        }

        return "";
    }

    private ApprovalStatus buildApprovalStatus(String eventType) {

        if ("APPROVED".equals(eventType)) {
            return ApprovalStatus.APPROVED;
        }
        if ("REJECTED".equals(eventType)) {
            return ApprovalStatus.REJECTED;
        }

        return null;
    }

    /**
     * 获取驳回备注逻辑
     *
     * @param formFields
     * @return
     */
    private String setApprovalComment(List<SubmitFormFieldDTO> formFields) {

        WfInflucerSubmitApprove influcerSubmitApprove = new WfInflucerSubmitApprove();

        try {

            Field remarkField = influcerSubmitApprove.getClass().getDeclaredField("remark");
            ExcelField annotation = remarkField.getAnnotation(ExcelField.class);
            String title = annotation.title();

            if (!StringUtils.isEmpty(title)) {
                for (SubmitFormFieldDTO formField : formFields) {
                    if (!StringUtils.isEmpty(formField.getCode()) && formField.getCode().equals(title)) {
                        return formField.getValue();
                    }
                }
            }
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    /**
     * 审批
     *
     * @param submitApprove
     * @param approvalRecord
     * @param approvalStatus
     * @param approvalComment
     */
    private String approvedOrRejectedApproval(WfInflucerSubmitApprove submitApprove,
                                              WfApprovalRecord approvalRecord,
                                              ApprovalStatus approvalStatus,
                                              String approvalComment) {

        Long requestId = submitApprove.getRequestId();
        //TODO 考虑会签，依次审批情况 暂不考虑

        Long nextStepId = approvalRecord.getNextStepId();
        Long stepId = approvalRecord.getStepId();
        WfStep step = wfStepService.getById(stepId);

        //拒绝
        if (approvalStatus.equals(ApprovalStatus.REJECTED)) {
            approvalRecord.setApprovalStatus(ApprovalStatus.REJECTED.getCode());
            approvalRecord.setApprovalStatusName(ApprovalStatus.REJECTED.getDescription());
            approvalRecord.setApprovalComment(approvalComment);
        }

        //同意
        if (approvalStatus.equals(ApprovalStatus.APPROVED)) {
            approvalRecord.setApprovalStatus(ApprovalStatus.APPROVED.getCode());
            approvalRecord.setApprovalStatusName(ApprovalStatus.APPROVED.getDescription());
        }

        Long userId = UserContextHolder.getUserId();
        if (null != userId) {
            approvalRecord.setApproverId(userId);
            approvalRecord.setApproverName(UserContextHolder.getUserName());
        }
        approvalRecord.setUpdateTime(new Date());

        //更新审批记录
        wfApprovalRecordService.updateById(approvalRecord);

        List<WfApprovalRequestView> view = new ArrayList<>();
        //到达最后一个节点
        if (nextStepId == null) {

            //更新审批单
            updateApproveOrders(requestId, approvalStatus.getCode(), approvalStatus.getDescription());

        } else {

            //设置下一个审批节点，为待审批状态

            //TODO 考虑当前节点多人审批情况 暂不考虑

            if (approvalStatus.equals(ApprovalStatus.APPROVED)) {

//                UpdateWrapper<WfApprovalRecord> updateWrapper = new UpdateWrapper<>();
//                updateWrapper.lambda().set(WfApprovalRecord::getApprovalStatus, ApprovalStatus.PENDING_SUBMIT.getCode())
//                        .eq(WfApprovalRecord::getRequestId, requestId)
//                        .eq(WfApprovalRecord::getStepId, nextStepId);
//                wfApprovalRecordService.update(updateWrapper);

                QueryWrapper<WfApprovalRecord> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda()
                        .eq(WfApprovalRecord::getRequestId, requestId)
                        .eq(WfApprovalRecord::getStepId, nextStepId);
                List<WfApprovalRecord> approvalRecords = wfApprovalRecordService.list(queryWrapper);
                for (WfApprovalRecord record : approvalRecords) {
                    wfApprovalRecordService.updateById(record);

                    view.add(new WfApprovalRequestView().setUserId(record.getApproverId()));
                }
            } else {
                view.add(new WfApprovalRequestView().setUserId(submitApprove.getCreateId()));
                updateApproveOrders(requestId, ApprovalStatus.REJECTED.getCode(), ApprovalStatus.REJECTED.getDescription());
            }
        }

        //更新提审单

        WfStep preStep = null;
        WfStep nextStep = null;

        if (approvalStatus.equals(ApprovalStatus.APPROVED)) {
            nextStep = null != nextStepId ? wfStepService.getById(nextStepId) : null;
            if (null != nextStep) {
                submitApprove.setStepId(nextStep.getId());
                submitApprove.setStatus(ApprovalStatus.PENDING_SUBMIT.getCode());
            }
        } else {
            //拒绝保存当前节点信息
            submitApprove.setStepId(step.getId());
            submitApprove.setStatus(approvalStatus.getCode());
        }

        //新的流程状态
        String statusName = wfStepService.getStatusName(step, nextStep, approvalStatus.getCode());
        submitApprove.setStatusName(statusName);
        submitApprove.setUpdateTime(new Date());

        //更新提审单信息
        updateById(submitApprove);

        //审批流状态通知
        submitApprove.setFlowName(step.getTypeName());
        messageSendHelper.operateRecord(submitApprove, UserContextHolder.getUserId());//WORKFLOW_SUBMIT  WORKFLOW_APPROVE
        //待审核提醒
        if (!CollectionUtils.isEmpty(view)) {
            messageSendHelper.generateStepApprovalRecords(submitApprove, view, UserContextHolder.getUserId());//WORKFLOW_SUBMIT  WORKFLOW_APPROVE
        }

        //最后一次审批额外逻辑
        if (approvalStatus.equals(ApprovalStatus.APPROVED) && null != nextStep) {

            //判断是否最后一个审批节点（不是最后一个节点）
            if (isLastApproveStep(nextStep)) {

                //最终同意，添加额外逻辑

                //项目-达人信息
                //settle_projects_influcer
                SettleProjectsInflucer settleProjectsInflucer = new SettleProjectsInflucer();
                settleProjectsInflucer.setSettleProjectsId(submitApprove.getProjectId());
                settleProjectsInflucer.setBizInflucerId(submitApprove.getInfluId());
                settleProjectsInflucerMapper.insert(settleProjectsInflucer);

                //结算表
                //settle_influencer_order

                //达人-非标分成比-合作平台信息维护
                BizInflucerProject bizInflucerProject = bizInflucerProjectService.lambdaQuery()
                        .eq(BizInflucerProject::getProjectId, submitApprove.getProjectId())
                        .eq(BizInflucerProject::getPlatformId, submitApprove.getPlatformId())
                        .eq(BizInflucerProject::getInflucerId, submitApprove.getInfluId())
                        .one();

                if (null != bizInflucerProject) {
                    bizInflucerProject.setInflucerRatio(bizInflucerProject.getInflucerRatio());
                    bizInflucerProject.setInflcplatid(submitApprove.getCoopId());
                    bizInflucerProject.setNickPlatid(submitApprove.getCoopName());
                    bizInflucerProject.setInflucerRatio(bizInflucerProject.getInflucerRatio());
                    bizInflucerProjectService.updateById(bizInflucerProject);
                } else {
                    bizInflucerProject = new BizInflucerProject();
                    bizInflucerProject.setProjectId(submitApprove.getProjectId());
                    bizInflucerProject.setInflucerId(submitApprove.getInfluId());
                    bizInflucerProject.setPlatformId(submitApprove.getPlatformId());
                    bizInflucerProject.setInflcplatid(submitApprove.getCoopId());
                    bizInflucerProject.setNickPlatid(submitApprove.getCoopName());
                    bizInflucerProject.setInflucerRatio(bizInflucerProject.getInflucerRatio());
                    bizInflucerProjectService.save(bizInflucerProject);
                }

                //提审单完成标记位
                submitApprove.setComplete(1);

                updateById(submitApprove);
                log.info("到达最后一个审批节点，审批完毕！！！");
            }
        }

        return "";
    }

    private WfStep getPrevStep(WfStep step) {

        Long typeId = step.getTypeId();
        Integer stepOrder = step.getStepOrder();

        QueryWrapper<WfStep> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WfStep::getTypeId, typeId)
                .lt(WfStep::getStepOrder, stepOrder)
                .orderByDesc(WfStep::getStepOrder);
        List<WfStep> preSteps = wfStepService.list(queryWrapper);

        if (CollectionUtils.isEmpty(preSteps)) {
            return null;
        }

        return preSteps.get(0);
    }

    private void updateApproveOrders(Long requestId, Integer approvalStatus, String approvalStatusName) {
        //审批单
        WfApprovalRequest approvalRequest = wfApprovalRequestService.getById(requestId);

        approvalRequest.setRequestStatus(approvalStatus);
        approvalRequest.setRequestStatusName(approvalStatusName);
        wfApprovalRequestService.updateById(approvalRequest);
    }


    private Boolean isLastApproveStep(WfStep step) {

        if (!StringUtils.isEmpty(step.getStepType()) && isNumeric(step.getStepType()) &&
                StepNodeType.EndNode.getCode().equals(Integer.parseInt(step.getStepType()))) {
            return true;
        }

        return false;
    }

    public static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        // 匹配整数或小数（包括负数）
        return str.matches("-?\\d+(\\.\\d+)?");
    }

    public String operateRecord(OperateRecordDTO dto) {

        WfInflucerSubmitApprove submitApprove = getById(dto.getId());

        if (null == submitApprove || null == OperateType.getType(dto.getOperateType())) {
            return "无效的数据请求！";
        }

        //审批数据由待提交-到-待审批
        //获取待审批记录
        QueryWrapper<WfApprovalRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WfApprovalRecord::getRequestId, submitApprove.getRequestId())
                .eq(WfApprovalRecord::getStepId, submitApprove.getStepId());
        List<WfApprovalRecord> list = wfApprovalRecordService.list(queryWrapper);

        WfStep wfStep = wfStepService.getById(submitApprove.getStepId());

        list.forEach(wfApprovalRecord -> {

            if (ApprovalStatus.REJECTED.getCode().equals(wfApprovalRecord.getApprovalStatus())) {

                //归档历史审批记录
                wfApprovalRecord.setHistory(1);
                wfApprovalRecordService.updateById(wfApprovalRecord);

                //保存新审批记录
                WfApprovalRecord newApprovalRecord = new WfApprovalRecord();
                BeanUtils.copyProperties(wfApprovalRecord, newApprovalRecord);
                newApprovalRecord.setId(null);
                newApprovalRecord.setApprovalStatus(ApprovalStatus.UNDER_REVIEW.getCode());
                newApprovalRecord.setApprovalStatusName(ApprovalStatus.UNDER_REVIEW.getDescription());
                newApprovalRecord.setApprovalComment(null);
                newApprovalRecord.setCreateTime(new Date());
                newApprovalRecord.setHistory(0);

                wfApprovalRecordService.save(newApprovalRecord);

                //更新提审记录
                submitApprove.setStatus(ApprovalStatus.UNDER_REVIEW.getCode());
                submitApprove.setStatusName("修改" + list.get(0).getStepName() + "待确认");

            } else {
                wfApprovalRecord.setUpdateTime(new Date());
                wfApprovalRecord.setApprovalStatus(ApprovalStatus.UNDER_REVIEW.getCode());
                wfApprovalRecordService.updateById(wfApprovalRecord);

                //更新提审记录
                submitApprove.setStatus(ApprovalStatus.UNDER_REVIEW.getCode());
                submitApprove.setStatusName(list.get(0).getStepName() + "待确认");
            }
        });

        //提审流待审批
        submitApprove.setFlowName(wfStep.getTypeName());
        messageSendHelper.operateRecord(submitApprove, UserContextHolder.getUserId());//WORKFLOW_SUBMIT  WORKFLOW_APPROVE
        updateById(submitApprove);

        return "";
    }

    public List<WfInflucerSubmitApproveExportVO> getExportData(String complete) {

        QueryWrapper<WfInflucerSubmitApprove> queryWrapper = new QueryWrapper<>();
        if (!StringUtils.isEmpty(complete) && "1".equals(complete)) {
            queryWrapper.lambda().eq(WfInflucerSubmitApprove::getComplete, 1);
        } else {
            queryWrapper.lambda().eq(WfInflucerSubmitApprove::getComplete, 0);
        }

        List<WfInflucerSubmitApprove> exportData = list(queryWrapper);

        if (!CollectionUtils.isEmpty(exportData)) {

            List<WfInflucerSubmitApproveExportVO> vos = new ArrayList<>();

            for (int i = 0; i < exportData.size(); i++) {

                WfInflucerSubmitApprove submitApprove = exportData.get(i);

                WfInflucerSubmitApproveExportVO exportVO = new WfInflucerSubmitApproveExportVO();

                BeanUtils.copyProperties(submitApprove, exportVO);

                exportVO.setNo(i + 1);//序号
                InflucerType influcerType = InflucerType.getType(submitApprove.getInflucerType());
                exportVO.setInflucerTypeName(null != influcerType ? influcerType.name : null);//达人类型

                vos.add(exportVO);
            }

            return vos;
        }

        return null;
    }
}
