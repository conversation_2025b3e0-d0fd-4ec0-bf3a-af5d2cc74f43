package com.th.cms.modular.bizClue.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.modular.bizClue.dto.BizClueUploadDto;
import com.th.cms.modular.bizClue.dto.BizClueUploadVo;
import com.th.cms.modular.bizClue.dto.BizClueUplodListParam;
import com.th.cms.modular.bizClue.dto.PageDto;
import com.th.cms.modular.bizClue.entity.BizClue;
import com.th.cms.modular.bizClue.entity.BizClueReceive;
import com.th.cms.modular.bizClue.entity.BizClueRelease;
import com.th.cms.modular.bizClue.entity.BizClueUpload;
import com.th.cms.modular.bizClue.enums.BizClueUploadStatusEnum;
import com.th.cms.modular.bizClue.mapper.BizClueUploadMapper;
import com.th.cms.modular.system.entity.Dept;
import com.th.cms.modular.system.entity.User;
import com.th.cms.modular.system.service.DeptService;
import com.th.cms.modular.system.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 达人质检测表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
public class BizClueUploadService extends ServiceImpl<BizClueUploadMapper, BizClueUpload> implements IService<BizClueUpload> {

    @Autowired
    private BizClueUploadMapper bizClueUploadMapper;

    @Autowired
    private BizClueService bizClueService;

    @Autowired
    private BizClueReceiveService bizClueReceiveService;

    @Autowired
    private BizClueReleaseService bizClueReleaseService;

    @Autowired
    private UserService userService;

    @Autowired
    private DeptService deptService;

    public static final String QC_LEADER = "质检组长";

    public static final String QC_MEMBER = "审核员";

    public static final String BIZ_LEADER = "商务组长";

    public static final String BIZ_MEMBER = "个人";

    public static final Integer TOTAL_TAKE_COUNT = 100;

    /**
     * 上传质检
     * @param dto
     */
    @Transactional
    public void upload(BizClueUploadDto dto) {
        User user = bizClueReceiveService.getUser();
        BizClueUpload upload = new BizClueUpload();
        upload.setUrl(dto.getUrl());
        upload.setFile(dto.getFile());
        upload.setStatus(BizClueUploadStatusEnum.WATING.getStatus());
        upload.setWxNum(dto.getWxNum());
        upload.setMobile(dto.getMobile());
        upload.setIsUpload(1);
        upload.setLeadsType(dto.getLeadsType());
        upload.setOwnerStaffId(user.getUserId());
        upload.setOwnerStaffName(user.getName());
        upload.setOwnerStaffGroupId(user.getDeptId());
        upload.setClueReceiveId(dto.getBizClueReceiveId());
        if (StringUtils.isNotBlank(dto.getUrl())){
            BizClue one = bizClueService.lambdaQuery().eq(BizClue::getUrl, dto.getUrl()).one();
            if (Objects.nonNull(one)){
                upload.setAvatar(one.getAvatar());
            }
        }
        if (Objects.nonNull(dto.getBizClueReceiveId())){
            Integer count = lambdaQuery().eq(BizClueUpload::getClueReceiveId, dto.getBizClueReceiveId()).count();
             Assert.isTrue(count < 1,"该线索已上传质检");
            BizClueReceive bizClueReceive = bizClueReceiveService.getById(dto.getBizClueReceiveId());
            BizClueRelease clueRelease = bizClueReleaseService.lambdaQuery().eq(BizClueRelease::getReceiveClueId, dto.getBizClueReceiveId()).one();
            boolean receiveExists = Objects.nonNull(bizClueReceive);
            Long clueId = receiveExists ?bizClueReceive.getClueId():clueRelease.getClueId();
            BizClue clue = bizClueService.getById(clueId);

            String remarks = receiveExists ?bizClueReceive.getRemarks():clueRelease.getRemarks();
            upload.setAccount(clue.getAccount());
            upload.setNickname(clue.getNickname());
            upload.setAccountId(clue.getAccountId());
            //这个groupId是质检组的groupId，领取的时候赋值
//            upload.setGroupId(groupId);
            upload.setPlatform(clue.getPlatform());
            upload.setExtendPlatform(clue.getExtendPlatform());
            upload.setIsRelease(Objects.nonNull(clueRelease)?1:0);
            upload.setRemarks(remarks);
            upload.setReleaseTime(Objects.nonNull(clueRelease)?clueRelease.getReleaseTime():null);
            upload.setIsUpload(2);
            if (receiveExists){
                BizClueReceive bizClueReceiveUpdate = new BizClueReceive();
                bizClueReceiveUpdate.setId(bizClueReceive.getId());
                bizClueReceiveUpdate.setMoveStatus("6");
                bizClueReceiveUpdate.setIsUpload(2);
                bizClueReceiveService.updateById(bizClueReceiveUpdate);

            }else {
                BizClueRelease releaseUpdate = new BizClueRelease();
                releaseUpdate.setId(clueRelease.getId());
                releaseUpdate.setMoveStatus("6");
                releaseUpdate.setIsUpload(2);
                bizClueReleaseService.updateById(releaseUpdate);
            }
        }
        save(upload);
    }

    /**
     * 领取质检
     * @param takeCount
     */
    public void take(Integer takeCount) {
        Assert.isTrue(takeCount <= TOTAL_TAKE_COUNT && TOTAL_TAKE_COUNT > 0, "单次领取数量限制{}条",TOTAL_TAKE_COUNT);
        List<Integer> idList = bizClueUploadMapper.takeList(takeCount);
        Assert.notEmpty(idList, "没有待领取质检数据");
        User user = userService.getUserByContext();
        boolean qcLeader = userService.isContainRoleName(user.getUserId(), QC_LEADER);
        boolean qcMember = userService.isContainRoleName(user.getUserId(), QC_MEMBER);
        Assert.isTrue(qcLeader || qcMember, "没有领取质检权限");
        bizClueUploadMapper.take(user,idList);
    }



    /**
     * 我的质检列表
     * @param param
     * @return
     */
    public Page<BizClueUpload> list(BizClueUplodListParam param) {
        User user = userService.getUserByContext();
        QueryWrapper<BizClueUpload> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<BizClueUpload> lambda = queryWrapper.lambda();
        lambda.eq(BizClueUpload::getIsDelete,0);
        if (userService.isContainRoleName(user.getUserId(),QC_LEADER)){
            lambda.eq(BizClueUpload::getGroupId,user.getDeptId());
        }else if (userService.isContainRoleName(user.getUserId(),BIZ_LEADER)){
//            lambda.in(BizClueUpload::getOwnerStaffId,userService.getUserIdsByDeptId(user.getDeptId()));
            //暂时商务组长只能查看自己的质检，暂时收回查看组员权限
            lambda.eq(BizClueUpload::getOwnerStaffId,user.getUserId());
        }else if (userService.isContainRoleName(user.getUserId(),QC_MEMBER)){
            lambda.eq(BizClueUpload::getReceiveId,user.getUserId());
        }else if (userService.isContainRoleName(user.getUserId(),BIZ_MEMBER)){
            lambda.eq(BizClueUpload::getOwnerStaffId,user.getUserId());
        }else {
            //没有这些角色的人不能查看
            return new Page<>();
        }
        if (StringUtils.isNotBlank(param.getKeyWords())){
            lambda.and(key->{
                key.like(BizClueUpload::getMobile,param.getKeyWords())
                        .or()
                        .like(BizClueUpload::getWxNum,param.getKeyWords());
                return key;
            });
        }
        if (StringUtils.isNotBlank(param.getLeadsType())){
            lambda.eq(BizClueUpload::getLeadsType,param.getLeadsType());
        }
        if (Objects.nonNull(param.getStatus())){
            lambda.eq(BizClueUpload::getStatus, param.getStatus());
        }
        if (Objects.nonNull(param.getUpdateTimeStart())){
            lambda.ge(BizClueUpload::getUpdateTime, param.getUpdateTimeStart());
        }
        if (Objects.nonNull(param.getUpdateTimeEnd())){
            lambda.le(BizClueUpload::getUpdateTime, param.getUpdateTimeEnd());
        }
        queryWrapper.orderByDesc("update_time");
        IPage<BizClueUpload> page = page(param, queryWrapper);
        List<Long> deptIds = page.getRecords().stream().map(BizClueUpload::getOwnerStaffGroupId).collect(Collectors.toList());
        Map<Long, Dept> deptIdMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(deptIds)){
            List<Dept> deptList = deptService.lambdaQuery().in(Dept::getDeptId, deptIds).list();
            deptIdMap = deptList.stream().collect(Collectors.toMap(Dept::getDeptId, Function.identity()));
        }
        for (BizClueUpload upload : page.getRecords()) {
            Dept dept = deptIdMap.get(upload.getOwnerStaffGroupId());
            if (Objects.nonNull(upload.getOwnerStaffId()) && Objects.nonNull(dept)) {
                upload.setOwnerStaffGroupName(dept.getFullName());
            }
        }
        Page<BizClueUpload> pageResult = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        pageResult.setRecords(page.getRecords());
        return pageResult;
    }


    public void release(Long ownerStaffId) {
        List<BizClueUpload> list = lambdaQuery().eq(BizClueUpload::getReceiveId, ownerStaffId)
                .in(BizClueUpload::getStatus, BizClueUploadStatusEnum.WATING.getStatus(), BizClueUploadStatusEnum.EDIT.getStatus())
                .list();
        if ( CollectionUtil.isEmpty(list)){
            return;
        }
        List<Integer> uploadIdList = list.stream().map(BizClueUpload::getId).collect(Collectors.toList());
        bizClueUploadMapper.clearReceiverId(uploadIdList);
    }

    public void delete(Integer id) {
        bizClueUploadMapper.softDelete(id);
    }

    public BizClueUploadVo info(Integer id) {
        BizClueUpload upload = lambdaQuery().eq(BizClueUpload::getId, id)
                .eq(BizClueUpload::getIsDelete, 0).one();
        BizClueUploadVo vo = new BizClueUploadVo();
        BeanUtils.copyProperties(upload,vo);
        if (Objects.nonNull(upload.getCheckId())){
            vo.setCheckName(userService.getById(upload.getCheckId()).getName());
        }
        return vo;
    }

    public void edit(BizClueUpload entity) {
        BizClueUpload byId = getById(entity.getId());
        Assert.notNull(byId, "数据不存在");
        Assert.isTrue(byId.getIsDelete() == 0, "数据已删除");
        Assert.isTrue(!StringUtils.equals(BizClueUploadStatusEnum.EDIT.getStatus() , byId.getStatus() )
                && !StringUtils.equals(BizClueUploadStatusEnum.APPROVED.getStatus(), byId.getStatus()),  "数据状态不正确");
        entity.setStatus(BizClueUploadStatusEnum.EDIT.getStatus());
        QueryWrapper<BizClueUpload> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizClueUpload::getId, entity.getId())
                .eq(BizClueUpload::getIsDelete, 0)
                .in( BizClueUpload::getStatus, BizClueUploadStatusEnum.WATING.getStatus(),BizClueUploadStatusEnum.REJECTED.getStatus());
        update(entity,queryWrapper);
        bizClueUploadMapper.clearReceiverId(Arrays.asList(entity.getId()));
    }

    @Transactional
    public void approve(Integer id) {
        BizClueUpload upload = getById(id);
        Assert.notNull(upload, "数据不存在");
        BizClueReceive receive = new BizClueReceive();
        receive.setIsSubmit(1);
        receive.setId(upload.getClueReceiveId());
        bizClueReceiveService.update(receive, new QueryWrapper<BizClueReceive>().lambda().eq(BizClueReceive::getId, upload.getClueReceiveId()));
        BizClueUpload uploadFinal = new BizClueUpload();
        uploadFinal.setCheckId(userService.getUserByContext().getUserId());
        uploadFinal.setStatus(BizClueUploadStatusEnum.APPROVED.getStatus());
        update(uploadFinal, new QueryWrapper<BizClueUpload>().lambda().eq(BizClueUpload::getId, id));
    }

    @Transactional
    public void reject(BizClueUploadDto dto) {
        Assert.notNull(dto.getId(), "id不能为空");
        BizClueUpload upload = getById(dto.getId());
        Assert.notNull(upload, "数据不存在");
        BizClueReceive receive = new BizClueReceive();
        receive.setIsSubmit(2);
        receive.setIsSubmitTime(LocalDateTime.now());
        receive.setId(upload.getClueReceiveId());
        bizClueReceiveService.update(receive, new QueryWrapper<BizClueReceive>().lambda().eq(BizClueReceive::getId, upload.getClueReceiveId()));
        BizClueUpload uploadFinal = new BizClueUpload();
        uploadFinal.setCheckId(userService.getUserByContext().getUserId());
        uploadFinal.setStatus(BizClueUploadStatusEnum.REJECTED.getStatus());
        uploadFinal.setRejectReason(dto.getRejectReason());
        update(uploadFinal, new QueryWrapper<BizClueUpload>().lambda().eq(BizClueUpload::getId, dto.getId()));
    }

    public List<User> qcList() {
        User user = userService.getUserByContext();
        Assert.isTrue(userService.isContainRoleName( user.getUserId(), QC_LEADER), "用户权限不足");
        Long deptId = user.getDeptId();
        return userService.getUsersByDeptId(deptId);
    }

    public Integer leftTakeCount() {
        return lambdaQuery().isNull(BizClueUpload::getReceiveId)
                .in(BizClueUpload::getStatus, BizClueUploadStatusEnum.EDIT.getStatus(),BizClueUploadStatusEnum.WATING.getStatus()).count();
    }
}
